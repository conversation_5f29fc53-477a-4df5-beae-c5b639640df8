import React, { useEffect, useState } from "react";
import "./css.css";
import {
  Descriptions,
  Divider,
  Tabs,
  Layout,
  Typography,
  Tag,
  Button,
  Flex,
  FloatButton,
  Timeline,
  Card,
  Row,
  Col,
  Progress,
  Badge,
  Space,
  Avatar,
} from "antd";
import {
  ScheduleOutlined,
  CarOutlined,
  UserOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  PrinterOutlined,
  TeamOutlined,
  FileTextOutlined,
  CalendarOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons";
import moment from "moment";
import PouchDB from "pouchdb";
import PrintComponents from "react-print-components";
import ViewTable from "../../../../Components/ViewTable";
import RenderBlob from "../../../../Components/RenderBlob";
import { PageHeader } from "@ant-design/pro-components";

const { Content } = Layout;
const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;

const Task = (props) => {
  const { data, pouchDatabase, databasePrefix, ...rest } = props;
  const [job, setJob] = useState(null);
  const [team, setTeam] = useState(null);
  const [company, setCompany] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch related data
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch job data
        if (data.job && data.job.value) {
          const jobsDB = new PouchDB("jobs");
          const jobData = await jobsDB.get(data.job.value);
          setJob(jobData);
        }

        // Fetch team data
        if (data.assigned_to && data.assigned_to.value) {
          const teamsDB = new PouchDB("teams");
          const teamData = await teamsDB.get(data.assigned_to.value);
          setTeam(teamData);
        }

        // Fetch company data
        const companyDB = new PouchDB("company");
        const companyData = await companyDB.get("company");
        setCompany(companyData);

        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        setLoading(false);
      }
    };

    fetchData();
  }, [data]);

  // Format the task status with appropriate tag color
  const getStatusTag = () => {
    const status = data.status || "default";
    let color = "blue";
    let text = "Draft";

    switch (status) {
      case "processing":
        color = "blue";
        text = "In Progress";
        break;
      case "success":
        color = "green";
        text = "Completed";
        break;
      case "error":
        color = "red";
        text = "Cancelled";
        break;
      default:
        color = "default";
        text = "Draft";
    }

    return <Tag color={color}>{text}</Tag>;
  };

  // Calculate progress percentage based on status
  const getProgressPercentage = () => {
    const status = data.status || "default";

    switch (status) {
      case "processing":
        return 50;
      case "success":
        return 100;
      case "error":
        return 100; // Still 100% for cancelled, but will be shown in red
      default:
        return 0;
    }
  };

  // Get progress status for the Progress component
  const getProgressStatus = () => {
    const status = data.status || "default";

    switch (status) {
      case "processing":
        return "active";
      case "success":
        return "success";
      case "error":
        return "exception";
      default:
        return "normal";
    }
  };

  // Get timeline items based on task status
  const getTimelineItems = () => {
    const status = data.status || "default";
    const items = [
      {
        color: "green",
        children: (
          <>
            <Text strong>Task Created</Text>
            <br />
            <Text type="secondary">
              {moment(data.createdAt || new Date()).format("DD MMM YYYY, HH:mm")}
            </Text>
          </>
        ),
      },
    ];

    if (status === "processing" || status === "success" || status === "error") {
      items.push({
        color: "blue",
        children: (
          <>
            <Text strong>In Progress</Text>
            <br />
            <Text type="secondary">
              {moment(data.startedAt || new Date()).format("DD MMM YYYY, HH:mm")}
            </Text>
          </>
        ),
      });
    }

    if (status === "success") {
      items.push({
        color: "green",
        children: (
          <>
            <Text strong>Completed</Text>
            <br />
            <Text type="secondary">
              {moment(data.completedAt || new Date()).format("DD MMM YYYY, HH:mm")}
            </Text>
          </>
        ),
      });
    } else if (status === "error") {
      items.push({
        color: "red",
        children: (
          <>
            <Text strong>Cancelled</Text>
            <br />
            <Text type="secondary">
              {moment(data.cancelledAt || new Date()).format("DD MMM YYYY, HH:mm")}
            </Text>
          </>
        ),
      });
    }

    return items;
  };

  // Printable task component
  const PrintableTask = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            <RenderBlob blob={company.logo} size={150} />
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>TASK DETAILS</Title>
            <Text>Task ID: {data._id}</Text>
            <br />
            <Text>Created: {moment(data.createdAt || new Date()).format("DD MMM YYYY")}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={4}>Task Information</Title>
      <Descriptions column={2} bordered>
        <Descriptions.Item label="Title" span={2}>{data.title}</Descriptions.Item>
        <Descriptions.Item label="Status">{getStatusTag()}</Descriptions.Item>
        <Descriptions.Item label="Due Date">
          {moment(data.due_date).format("DD MMM YYYY, HH:mm")}
        </Descriptions.Item>
        <Descriptions.Item label="Description" span={2}>
          {data.description || "No description provided"}
        </Descriptions.Item>
      </Descriptions>

      <Divider />

      {job && (
        <>
          <Title level={4}>Related Job</Title>
          <Descriptions column={2} bordered>
            <Descriptions.Item label="Job ID">{job._id}</Descriptions.Item>
            <Descriptions.Item label="Customer">
              {job.customer ? job.customer.label : ""}
            </Descriptions.Item>
            <Descriptions.Item label="Vehicle">
              {job.vehicle ? job.vehicle.label : ""}
            </Descriptions.Item>
            <Descriptions.Item label="Job Status">
              {job.status || ""}
            </Descriptions.Item>
          </Descriptions>
          <Divider />
        </>
      )}

      {team && (
        <>
          <Title level={4}>Assigned Team</Title>
          <Descriptions column={2} bordered>
            <Descriptions.Item label="Team Name">{team.name}</Descriptions.Item>
            <Descriptions.Item label="Team Leader">
              {team.leader ? team.leader.label : ""}
            </Descriptions.Item>
            <Descriptions.Item label="Team Members" span={2}>
              {team.members && team.members.map(member => member.label).join(", ")}
            </Descriptions.Item>
          </Descriptions>
        </>
      )}

      <Divider />

      <div style={{ marginTop: "50px", display: "flex", justifyContent: "space-between" }}>
        <div>
          <p>Assigned By: _________________________</p>
        </div>
        <div>
          <p>Completed By: _________________________</p>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return <div>Loading task information...</div>;
  }

  return (
    <>
      <PageHeader
        backIcon={<ScheduleOutlined style={{ fontSize: 30 }} />}
        onBack={() => window.history.back()}
        title={data.title}
        subTitle={job ? `Job: ${job.customer?.label}'s ${job.vehicle?.label}` : ""}
        tags={getStatusTag()}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Task
              </Button>
            }
          >
            <PrintableTask />
          </PrintComponents>,
        ]}
      />

      <Content style={{ padding: "0 24px 24px" }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={16}>
            <Card title={<><InfoCircleOutlined /> Task Details</>} className="task-card">
              <Descriptions bordered column={{ xxl: 2, xl: 2, lg: 2, md: 1, sm: 1, xs: 1 }}>
                <Descriptions.Item label="Title" span={2}>{data.title}</Descriptions.Item>
                <Descriptions.Item label="Status">{getStatusTag()}</Descriptions.Item>
                <Descriptions.Item label="Due Date">
                  <CalendarOutlined /> {moment(data.due_date).format("DD MMM YYYY, HH:mm")}
                </Descriptions.Item>
                <Descriptions.Item label="Description" span={2}>
                  {data.description ? (
                    <Paragraph>{data.description}</Paragraph>
                  ) : (
                    <Text type="secondary">No description provided</Text>
                  )}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card title={<><TeamOutlined /> Assigned Team</>} className="task-card">
              {team ? (
                <>
                  <Flex align="center" gap="middle" style={{ marginBottom: 16 }}>
                    <Avatar.Group>
                      <Avatar icon={<UserOutlined />} />
                      <Avatar style={{ backgroundColor: '#f56a00' }}>T</Avatar>
                      <Avatar style={{ backgroundColor: '#87d068' }}>M</Avatar>
                    </Avatar.Group>
                    <div>
                      <Text strong>{team.name}</Text>
                      <br />
                      <Text type="secondary">Led by {team.leader?.label}</Text>
                    </div>
                  </Flex>
                  <Divider style={{ margin: '12px 0' }} />
                  <Text strong>Team Members:</Text>
                  <ul className="team-members-list">
                    {team.members && team.members.map((member, index) => (
                      <li key={index}>{member.label}</li>
                    ))}
                  </ul>
                </>
              ) : (
                <Text type="secondary">No team assigned</Text>
              )}
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col xs={24} lg={16}>
            <Card title={<><CarOutlined /> Related Job</>} className="task-card">
              {job ? (
                <Descriptions bordered column={{ xxl: 2, xl: 2, lg: 2, md: 1, sm: 1, xs: 1 }}>
                  <Descriptions.Item label="Job ID">{job._id}</Descriptions.Item>
                  <Descriptions.Item label="Customer">
                    {job.customer ? job.customer.label : ""}
                  </Descriptions.Item>
                  <Descriptions.Item label="Vehicle">
                    {job.vehicle ? job.vehicle.label : ""}
                  </Descriptions.Item>
                  <Descriptions.Item label="Job Status">
                    <Tag color={job.status === "open" ? "green" : "blue"}>
                      {job.status || ""}
                    </Tag>
                  </Descriptions.Item>
                </Descriptions>
              ) : (
                <Text type="secondary">No job associated with this task</Text>
              )}
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card title={<><ClockCircleOutlined /> Task Progress</>} className="task-card">
              <Progress
                percent={getProgressPercentage()}
                status={getProgressStatus()}
                strokeWidth={10}
              />
              <Divider style={{ margin: '16px 0' }} />
              <Timeline
                mode="left"
                items={getTimelineItems()}
              />
            </Card>
          </Col>
        </Row>
      </Content>

      <FloatButton
        icon={<PrinterOutlined />}
        type="primary"
        tooltip="Print Task"
        onClick={() => document.getElementById("print-button").click()}
        style={{ right: 94 }}
      />

      {/* Hidden print button for FloatButton to trigger */}
      <div style={{ display: "none" }}>
        <PrintComponents
          trigger={<Button id="print-button">Print</Button>}
        >
          <PrintableTask />
        </PrintComponents>
      </div>
    </>
  );
};

export default Task;
