const { jobs } = require("./jobs");

exports.quotations = {
  name: "Quotations",
  icon: "QuestionCircleOutlined",
  path: "/quotations",
  collection: "quotations",
  singular: "Quotation",
  columns: [
    {
      dataIndex: "_id",
      title: "ID",
      hideInForm: true,
    },
        { valueType: "date", dataIndex: "date", title: "Date", width: "lg" },
        {
          type: "dbSelect",
          dataIndex: "job",
          collection: jobs.collection,
          title: "Job",
          label: ["customer.label", "-", "vehicle.label"],
          width: "lg",
        },
    {
      title: "Required parts and services",
      valueType: "formList",
      isRequired: true,
      fieldProps: {
        initialValue: [{}],
        creatorButtonProps: {
          creatorButtonText: "Add Item",
          onClick: (e, { add, form }) => {
            e.preventDefault();
            add({});  // Use the add method provided by FormList
          }
        }
      },
      dataIndex: "items",
      hideInTable: true,
      columns: [
        {
          valueType: "group",

          columns: [
            {
              title: "Item",
              dataIndex: "item",
              type: "dbSelect",
              collection: "parts_and_services",
              label: ["name"],
              isRequired: true,
              colProps: {
                md: 9,
              },
            },
            {
              title: "Quantity",
              dataIndex: "quantity",
              valueType: "digit",
              isRequired: true,
              colProps: {
                md: 5,
              },
            },
            {
              title: "Price",
              dataIndex: "price",
              valueType: "money",
              isRequired: true,
              isPrintable: true,
              colProps: {
                md: 5,
              },
            },
            {
              valueType: "dependency",
              fieldProps: {
                name: ["price", "quantity"],
              },
              columns: ({ price, quantity }) => {
                return [
                  {
                    title: "Sub Total",
                    dataIndex: "subTotal",
                    colProps: {
                      md: 5,
                    },
                    fieldProps: {
                      disabled: true,
                      value: (price ? price : 0) * (quantity ? quantity : 0),
                    },
                  },
                ];
              },
            },
          ],
        },
      ],
    },
    {
      title: "Discounted",
      dataIndex: "discounted",
      valueType: "switch",
      fieldProps: {
        unCheckedChildren: "No",
        checkedChildren: "Yes",
      },
      colProps: {
        md: 2,
        offset: 14,
      },
      hideInTable: true,
    },
    {
      title: "Taxable",
      dataIndex: "taxable",
      valueType: "switch",
      fieldProps: {
        unCheckedChildren: "No",
        checkedChildren: "Yes",
      },
      colProps: {
        md: 2,
      },
      hideInTable: true,
    },
    {
      valueType: "dependency",
      fieldProps: {
        name: ["items", "taxable", "discounted", "discount"],
      },

     
      columns: ({ items, taxable, discounted, discount }) => {
        const itemValue = (items || []).reduce(
          (pv, cv) =>
            pv + (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
          0
        );

        const docDiscount = discounted ? discount : 0;

        const tax = taxable ? itemValue * 0.18 : 0;

        return [
          {
            title: "Total",
            dataIndex: "total",
            colProps: {
              md: 6,
              // offset: 18,
            },
            fieldProps: {
              disabled: true,
              value: itemValue,
            },
            // valueType: "digit",
          },
        ];
      },
    },

    {
      valueType: "dependency",
      hideInTable: true,
      name: ["taxable", "items", "discounted"],
      columns: ({ taxable, items, discounted }) => {
        let columnsToRender = [];

        if (discounted && items && items.length > 0) {
          columnsToRender.push({
            dataIndex: "discount_description",
            title: "Discount Description",
            colProps: {
              md: 6,
              offset: !taxable ? 12 : 6,
            },
          });
          columnsToRender.push({
            dataIndex: "discount",
            title: "Discount",
            colProps: {
              md: 6,
              // offset: !taxable ? 18 : 12,
            },
          });
        }

        if (taxable && items && items.length > 0) {
          columnsToRender.push({
            dataIndex: "tax",
            title: "Tax",
            colProps: {
              md: 6,
              offset: !discounted ? 18 : null,
            },
            fieldProps: {
              disabled: true,
              value:
                (items || []).reduce(
                  (pv, cv) =>
                    pv +
                    (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
                  0
                ) * 0.18,
            },
          });
        }

        return columnsToRender;
      },
    },
    {
      title: "Total",
      dataIndex: "total",
      colProps: {
        md: 6,
        offset: 18,
      },
      hideInForm: true,

      fieldProps: {
        name: ["items", "taxable", "discounted", "discount"],
        value: ({ items, taxable, discounted, discount }) => {
          const itemValue = (items || []).reduce(
            (pv, cv) =>
              pv + (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
            0
          );

          const docDiscount = discounted ? discount : 0;

          const tax = taxable ? itemValue * 0.18 : 0;

          return itemValue - docDiscount + tax;
        },

      },
      render:( record, { items, taxable } )=> (taxable
          ? (items || []).reduce(
          (pv, cv) =>
            pv + (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
          0
        )* 1.18
          : (items || []).reduce(
          (pv, cv) =>
            pv + (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
          0
        )).toLocaleString(),
      
      valueType: "text",
    },
    {
      valueType: "textarea",
      dataIndex: "description",
      hideInTable: true,
      title: "Description, Remarks or Notes",
    },
  ],
};
