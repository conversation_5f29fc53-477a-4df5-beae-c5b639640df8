import React, { useEffect, useState } from "react";
import "./css.css";
import {
  Descriptions,
  Divider,
  Tabs,
  Layout,
  Typography,
  Tag,
  Button,
  Flex,
  FloatButton,
} from "antd";
import {
  CarOutlined,
  UserOutlined,
  FieldTimeOutlined,
  PrinterOutlined,
} from "@ant-design/icons";
import moment from "moment";
import PrintComponents from "react-print-components";
import ViewTable from "../../../../Components/ViewTable";
import RenderBlob from "../../../../Components/RenderBlob";
import { PageHeader } from "@ant-design/pro-components";

const { Content } = Layout;
const { TabPane } = Tabs;
const { Title, Text } = Typography;

const Booking = (props) => {
  const { data, pouchDatabase, databasePrefix, ...rest } = props;
  const [customer, setCustomer] = useState(null);
  const [vehicle, setVehicle] = useState(null);
  const [company, setCompany] = useState(null);
  const [loading, setLoading] = useState(true);

  // Shared props for ViewTable components
  const sharedProps = {
    pouchDatabase: props.pouchDatabase,
    databasePrefix: props.databasePrefix,
    CRUD_USER: props.CRUD_USER,
    userPermissions: props.userPermissions,
    modules: props.modules,
    modulesProperties: props.modulesProperties,
    filterID: { column: "booking", id: data._id },
  };

  // Fetch related data
  useEffect(() => {
    const fetchData = async () => {
      if (!pouchDatabase || databasePrefix === undefined) {
        setLoading(false);
        return;
      }

      try {
        // Fetch customer data
        if (data.customer && data.customer.value) {
          const customerData = await pouchDatabase("customers", databasePrefix).getDocument(data.customer.value);
          setCustomer(customerData);
        }

        // Fetch vehicle data
        if (data.vehicle && data.vehicle.value) {
          const vehicleData = await pouchDatabase("vehicles", databasePrefix).getDocument(data.vehicle.value);
          setVehicle(vehicleData);
        }

        // Fetch company data
        const companyData = await pouchDatabase("organizations", databasePrefix).getAllData();
        if (companyData && companyData.length > 0) {
          setCompany(companyData[0]);
        }

        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        setLoading(false);
      }
    };

    fetchData();
  }, [data, pouchDatabase, databasePrefix]);

  // Format the booking status with appropriate tag color
  const getStatusTag = () => {
    const status = data.status || "Pending";
    let color = "blue";

    switch (status.toLowerCase()) {
      case "confirmed":
        color = "green";
        break;
      case "cancelled":
        color = "red";
        break;
      case "completed":
        color = "purple";
        break;
      default:
        color = "blue";
    }

    return <Tag color={color}>{status}</Tag>;
  };

  // Printable booking component
  const PrintableBooking = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            <RenderBlob blob={company.logo} size={150} />
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>BOOKING CONFIRMATION</Title>
            <Text>Booking ID: {data._id}</Text>
            <br />
            <Text>Date: {moment(data.reporting_date).format("DD MMM YYYY")}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={4}>Customer Information</Title>
      {customer && (
        <Descriptions column={2} bordered>
          <Descriptions.Item label="Name">{customer.name}</Descriptions.Item>
          <Descriptions.Item label="Phone">{customer.phone}</Descriptions.Item>
          {customer.email && (
            <Descriptions.Item label="Email">{customer.email}</Descriptions.Item>
          )}
          {customer.address && (
            <Descriptions.Item label="Address">{customer.address}</Descriptions.Item>
          )}
        </Descriptions>
      )}

      <Divider />

      <Title level={4}>Vehicle Information</Title>
      {vehicle && (
        <Descriptions column={2} bordered>
          <Descriptions.Item label="Make & Model">
            {vehicle.make} {vehicle.modal}
          </Descriptions.Item>
          <Descriptions.Item label="Registration">
            {vehicle.reg_number}
          </Descriptions.Item>
          {vehicle.year && (
            <Descriptions.Item label="Year">{vehicle.year}</Descriptions.Item>
          )}
          {vehicle.color && (
            <Descriptions.Item label="Color">{vehicle.color}</Descriptions.Item>
          )}
        </Descriptions>
      )}

      <Divider />

      <Title level={4}>Booking Details</Title>
      <Descriptions column={2} bordered>
        <Descriptions.Item label="Reporting Date">
          {moment(data.reporting_date).format("DD MMM YYYY")}
        </Descriptions.Item>
        <Descriptions.Item label="Delivery By">
          {data.delivery}
        </Descriptions.Item>
        <Descriptions.Item label="Status" span={2}>
          {data.status || "Pending"}
        </Descriptions.Item>
        <Descriptions.Item label="Problem Statement" span={2}>
          {data.problem_statement}
        </Descriptions.Item>
      </Descriptions>

      <Divider />

      <div style={{ marginTop: "50px", display: "flex", justifyContent: "space-between" }}>
        <div>
          <p>Customer Signature: _________________________</p>
        </div>
        <div>
          <p>Authorized Signature: _________________________</p>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return <div>Loading booking information...</div>;
  }

  return (
    <>
      <PageHeader
        backIcon={<FieldTimeOutlined style={{ fontSize: 30 }} />}
        onBack={() => window.history.back()}
        title={`Booking: ${data._id}`}
        subTitle={customer ? customer.name : ""}
        tags={getStatusTag()}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Booking
              </Button>
            }
          >
            <PrintableBooking />
          </PrintComponents>,
        ]}
      />

      <Content style={{ padding: "0 24px 24px" }}>
        <Flex gap="middle" vertical>
          <div>
            <Title level={4}>
              <UserOutlined /> Customer Information
            </Title>
            {customer && (
              <Descriptions bordered column={{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }}>
                <Descriptions.Item label="Name">{customer.name}</Descriptions.Item>
                <Descriptions.Item label="Phone">{customer.phone}</Descriptions.Item>
                {customer.email && (
                  <Descriptions.Item label="Email">{customer.email}</Descriptions.Item>
                )}
                {customer.address && (
                  <Descriptions.Item label="Address">{customer.address}</Descriptions.Item>
                )}
              </Descriptions>
            )}
          </div>

          <div>
            <Title level={4}>
              <CarOutlined /> Vehicle Information
            </Title>
            {vehicle && (
              <Descriptions bordered column={{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }}>
                <Descriptions.Item label="Make & Model">
                  {vehicle.make} {vehicle.modal}
                </Descriptions.Item>
                <Descriptions.Item label="Registration">
                  {vehicle.reg_number}
                </Descriptions.Item>
                {vehicle.year && (
                  <Descriptions.Item label="Year">{vehicle.year}</Descriptions.Item>
                )}
                {vehicle.color && (
                  <Descriptions.Item label="Color">{vehicle.color}</Descriptions.Item>
                )}
                {vehicle.chasis_number && (
                  <Descriptions.Item label="Chassis Number">
                    {vehicle.chasis_number}
                  </Descriptions.Item>
                )}
                {vehicle.engine_number && (
                  <Descriptions.Item label="Engine Number">
                    {vehicle.engine_number}
                  </Descriptions.Item>
                )}
              </Descriptions>
            )}
          </div>

          <div>
            <Title level={4}>
              <FieldTimeOutlined /> Booking Details
            </Title>
            <Descriptions bordered column={{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }}>
              <Descriptions.Item label="Reporting Date">
                {moment(data.reporting_date).format("DD MMM YYYY")}
              </Descriptions.Item>
              <Descriptions.Item label="Delivery By">
                {data.delivery}
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                {getStatusTag()}
              </Descriptions.Item>
              <Descriptions.Item label="Problem Statement" span={3}>
                {data.problem_statement}
              </Descriptions.Item>
            </Descriptions>
          </div>
        </Flex>

        <Divider />

        <Tabs defaultActiveKey="jobs">
          <TabPane
            tab={
              <span>
                <FieldTimeOutlined /> Related Jobs
              </span>
            }
            key="jobs"
          >
            <ViewTable
              {...sharedProps}
              {...props.modules.jobs}
              {...props.modulesProperties.jobs}
            />
          </TabPane>
        </Tabs>
      </Content>

      <FloatButton
        icon={<PrinterOutlined />}
        type="primary"
        tooltip="Print Booking"
        onClick={() => document.getElementById("print-button").click()}
        style={{ right: 94 }}
      />

      {/* Hidden print button for FloatButton to trigger */}
      <div style={{ display: "none" }}>
        <PrintComponents
          trigger={<Button id="print-button">Print</Button>}
        >
          <PrintableBooking />
        </PrintComponents>
      </div>
    </>
  );
};

export default Booking;
