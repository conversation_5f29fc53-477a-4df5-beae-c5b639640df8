import React, { useEffect, useState } from "react";
import ReferenceNumber from "../../../../Components/ReferenceNumber";
import {
  Button,
  Modal,
  Typography,
  Drawer,
  Flex,
  Descriptions,
  Divider,
  Float<PERSON>utton,
  Card,
} from "antd";
import moment from "moment";
import PrintComponents from "react-print-components";
import {
  AuditOutlined,
  PrinterOutlined,
  CheckSquareTwoTone,
  CloseSquareTwoTone,
} from "@ant-design/icons";
import DocumentHead from "../../../Universal/CustomViews/Components/DocumentHead";

const JobCardTemplate = (props) => {
  const { record, pouchDatabase, databasePrefix } = props;

  

  const [customer, setCustomer] = useState(null);
  const [vehicle, setVehicle] = useState(null);
  const [jobInvoices, setJobInvoices] = useState([]);
  const [parts_and_services, setPartsAndServices] = useState([]);

  const checkListItems = [
    "Tools",
    "Jack",
    "Mats",
    "Radio",
    "Wheel Caps",
    "Side Mirrors",
    "Spare Tire",
    "Mad Flap",
  ];

  useEffect(() => {
    if (record.customer?.value && pouchDatabase && databasePrefix !== undefined) {
      pouchDatabase("customers", databasePrefix)
        .getDocument(record.customer.value)
        .then((res) => {
          setCustomer(res);
        })
        .catch((err) => {
          console.error("Error fetching customer:", err);
        });
    }
  }, [record.customer?.value, pouchDatabase, databasePrefix]);

  useEffect(() => {
    if (record.vehicle?.value && pouchDatabase && databasePrefix !== undefined) {
      pouchDatabase("vehicles", databasePrefix)
        .getDocument(record.vehicle.value)
        .then((res) => {
          setVehicle(res);
        })
        .catch((err) => {
          console.error("Error fetching vehicle:", err);
        });
    }
  }, [record.vehicle?.value, pouchDatabase, databasePrefix]);

  useEffect(() => {
    if (record._id && pouchDatabase && databasePrefix !== undefined) {
      pouchDatabase("invoices", databasePrefix)
        .getAllData()
        .then((docs) => {
          // Convert to the expected format with rows structure and filter by job
          const filteredInvoices = docs
            .filter((doc) => doc.job?.value === record._id)
            .map(doc => ({ doc }));
          setJobInvoices(filteredInvoices);
        })
        .catch((err) => {
          console.error("Error fetching invoices:", err);
        });
    }
  }, [record._id, pouchDatabase, databasePrefix]);

  useEffect(() => {
    if (pouchDatabase && databasePrefix !== undefined) {
      pouchDatabase("parts_and_services", databasePrefix)
        .getAllData()
        .then((docs) => {
          // Convert to the expected format with rows structure
          const rows = docs.map(doc => ({ doc }));
          setPartsAndServices(rows);
        })
        .catch((err) => {
          console.error("Error fetching parts and services:", err);
        });
    }
  }, [pouchDatabase, databasePrefix]);

  

  return (
    <>
      <DocumentHead logoSize={250} />
      <Flex direction="column" justify="space-between">
        <div style={{ width: "30%" }}></div>
        <div
          style={{
            textAlign: "center",
            fontSize: 25,
            width: "40%",
            fontWeight: "bold",
          }}
        >
          JOB CARD
        </div>
        <div style={{ width: "30%", textAlign: "right" }}>
          <strong>Job Ref: </strong>
          {record.referenceNumber ? (
            <ReferenceNumber value={record.referenceNumber} />
          ) : (
            record._id
          )}
          <br />
          <strong>Date: </strong>
          {jobInvoices[0]?.doc?.date ? moment(jobInvoices[0].doc.date).format("DD MMM YY") : moment().format("DD MMM YY")}
          {record.millage && (
            <>
              <br />
              <strong>Mileage: </strong>
              {record.millage.toLocaleString()} km
            </>
          )}
        </div>
      </Flex>
      {/* customer details */}

      {customer && vehicle && (
        <Descriptions
          bordered
          size="large"
          column={3}
          style={{ marginTop: 20 }}
        >
          <Descriptions.Item label="Customer Name">
            {customer.name}
          </Descriptions.Item>
          <Descriptions.Item label="Phone">
            {customer.phone} {customer.mobile && "/" + customer.mobile}
          </Descriptions.Item>
          <Descriptions.Item label="Address">
            {customer.address}
          </Descriptions.Item>

          <Descriptions.Item label="Vehicle" span={1}>
            {`${vehicle.make} ${vehicle.modal}`}
          </Descriptions.Item>
          <Descriptions.Item label="Number Plate" span={1}>
            {vehicle.reg_number}
          </Descriptions.Item>
          <Descriptions.Item label="Mileage" span={1}>
            {record.millage ? `${record.millage.toLocaleString()} km` : 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="Problem Reported" span={3}>
            {record.problem_statement && record.problem_statement}
          </Descriptions.Item>
        </Descriptions>
      )}
      {
        // <Flex>
        <Card style={{ marginTop: 20 }}>
          <Card.Grid hoverable={false} style={{ width: `50%`, minHeight: 200 }}>
            <Typography.Title level={5}>Faults Diagnosed</Typography.Title>
            <Divider />
            <ol style={{ margin: 0 }}>
              {["Body Report", "Mechanical Report", "Electrical Report"].map(
                (r) => {
                  return (
                    record[r] &&
                    record[r].map((item, i) => {
                      return item.report && <li key={i}>{item.report}</li>;
                    })
                  );
                }
              )}
            </ol>
          </Card.Grid>
          <Card.Grid hoverable={false} style={{ width: `50%`, minHeight: 200 }}>
            <Typography.Title level={5}>Work Done</Typography.Title>
            <Divider />
            {jobInvoices.length > 0 && (
              <ol style={{ margin: 0 }}>
                {jobInvoices.map(
                  (item, i) =>
                    item.doc &&
                    item.doc.items &&
                    item.doc.items.length > 0 &&
                    item.doc.items.map(
                      (item, i) =>
                        item.item &&
                        parts_and_services.find((p) => p.id === item.item.value)
                          ?.doc?.item_type === "service" && (
                          <li key={i}>{item.item.label}</li>
                        )
                    )
                )}
              </ol>
            )}
          </Card.Grid>
          <Card.Grid hoverable={false} style={{ width: `50%`, minHeight: 200 }}>
            <Typography.Title level={5}>Parts Fitted</Typography.Title>
            <Divider />
            {jobInvoices.length > 0 && (
              <ol style={{ margin: 0 }}>
                {jobInvoices.map(
                  (item, i) =>
                    item.doc &&
                    item.doc.items &&
                    item.doc.items.length > 0 &&
                    item.doc.items.map(
                      (item, i) =>
                        item.item &&
                        parts_and_services.find((p) => p.id === item.item.value)
                          ?.doc.item_type === "part" && (
                          <li key={i}>
                            {item.quantity} {item.item.label}
                          </li>
                        )
                    )
                )}
              </ol>
            )}
          </Card.Grid>
          <Card.Grid hoverable={false} style={{ width: `50%`, minHeight: 200 }}>
            <Typography.Title level={5}>Old Parts Returned</Typography.Title>
            <Divider />
            {
              record.returnedParts ?
                (
                  <ol style={{ margin: 0 }}>
                    {record.returnedParts.map((item, i) => (
                      <li key={i}>{item}</li>
                    ))}
                  </ol>
                )
                :
                jobInvoices.length > 0 && (
                  <ol style={{ margin: 0 }}>
                    {jobInvoices.map(
                      (item, i) =>
                        item.doc &&
                        item.doc.items &&
                        item.doc.items.length > 0 &&
                        item.doc.items.map(
                          (item, i) =>
                            item.item &&
                            parts_and_services.find((p) => p.id === item.item.value)
                              ?.doc.item_type === "part" && (
                              <li key={i}>
                                {item.quantity} {item.item.label}
                              </li>
                            )
                        )
                    )}
                  </ol>
                )}
          </Card.Grid>
        </Card>
        // </Flex>
      }
      {record.other_details && (
        <p style={{ marginTop: 15 }}>
          <strong>Other Details : </strong>
          {record.other_details}
        </p>
      )}
      {record.next_appointment_date && (
        <p style={{ marginTop: 15 }}>
          <strong>Next Appointment Date : </strong>
          {moment(record.next_appointment_date).format("DD MMM YYYY")}
        </p>
      )}
      {record.checklist ? (
        <Flex
          direction="column"
          justify="space-evenly"
          style={{ margin: 20, fontSize: 11 }}
        >
          {checkListItems.map((c, i) => {
            return (
              <Flex key={i} gap={5} justify="center" align="center" vertical>
                <div>{c}</div>
                {record.checklist.includes(i.toString()) ? (
                  <CheckSquareTwoTone
                    style={{ fontSize: "40px" }}
                    twoToneColor="#52c41a"
                  />
                ) : (
                  <CloseSquareTwoTone
                    style={{ fontSize: "40px" }}
                    twoToneColor="#eb2f96"
                  />
                )}
              </Flex>
            );
          })}
        </Flex>
      ) : // })
        // record.checklist.map((c, i) => {
        //     return (
        //       <>
        //         <CheckSquareTwoTone
        //           style={{ fontSize: "30px" }}
        //           twoToneColor="#52c41a"
        //         />
        //         <CloseSquareTwoTone
        //           style={{ fontSize: "30px" }}
        //           twoToneColor="#eb2f96"
        //         />
        //       </>
        //     );
        //   }
        // )
        null}
      {record.proposed_completion_date && (
        <div>
          <strong>Proposed Completion Date: </strong>
          {moment(record.proposed_completion_date).format("DD MMM YY")}
        </div>
      )}
      <div style={{ width: "100%", padding: 0, margin: 0, marginTop: 20, fontSize: 12 }}>
        <table style={{ width: "100%", borderCollapse: "collapse", }}>
          <thead>
            <tr>
              <th style={styles.th}>Role</th>
              <th style={{ ...styles.th, width: "40%" }}>Name</th>
              <th style={styles.th}>Signature</th>
              <th style={styles.th}>Date</th>
            </tr>
          </thead>
          <tbody>
            {["Workshop Manager", "Chief Mechanic", "Customer"].map((role, index) => (
              <tr key={index}>
                <td style={styles.td}>{role}</td>
                <td style={styles.td}></td>
                <td style={styles.td}></td>
                <td style={styles.td}></td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </>
  );
};

const JobCard = (props) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const showModal = () => {
    setIsModalOpen(true);
  };

  const handleOk = () => {
    setIsModalOpen(false);
  };

  const handleCancel = () => {
    setIsModalOpen(false);
  };
  return (
    <>
      <Typography.Text type="primary" onClick={showModal}>
        <AuditOutlined /> Job Card
      </Typography.Text>
      <Drawer
        title="Basic Modal"
        open={isModalOpen}
        // onOk={handleOk}
        onClose={handleCancel}
        width={1000}
      >
        <PrintComponents
          trigger={
            <FloatButton
              icon={<PrinterOutlined />}
              tooltip={<div>Print</div>}
            />
          }
        >
          <JobCardTemplate {...props} />
        </PrintComponents>
        <JobCardTemplate {...props} />
      </Drawer>
    </>
  );
};

const styles = {
  th: {
    border: "1px solid rgba(5, 5, 5, 0.06)",
    padding: "5px",
    backgroundColor: "#f2f2f2",
  },
  td: {
    border: "1px solid rgba(5, 5, 5, 0.06)",
    padding: "5px",
  },
};

export default JobCard;
