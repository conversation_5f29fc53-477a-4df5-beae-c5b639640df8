const { jobs } = require("./jobs");
const { items } = require("./utils");

exports.invoices = {
  name: "Invoices",
  icon: "PoundOutlined",
  path: "/invoices",
  collection: "invoices",
  singular: "Invoice",
  columns: [
    {
      dataIndex: "_id",
      title: "ID",
      hideInForm: true,
    },

    { valueType: "date", dataIndex: "date", title: "Date", width: "lg" },
    {
      type: "dbSelect",
      dataIndex: "job",
      collection: jobs.collection,
      title: "Job",
      label: ["customer.label", "-", "vehicle.label", "[", "_id", "]"],
      width: "lg",
      isPrintable: true,
    },
    {
      title: "Customer Type",
      dataIndex: "customer_type",
      valueType: "select",
      filters: true,
      onFilter: true,
      valueEnum: {
        Individual: {
          text: "Individual",
          status: "error"
        },
        Company: {
          text: "Company",
          status: "success"
        },
      },
      colProps: {
        md: 8,
      },
      hideInForm: true,
    },
    {
      title: "Phone",
      valueType: "text",
      dataIndex: "phone",
      hideInForm: true
    },
    {
      title: "Required parts and services",
      valueType: "formList",
      isRequired: true,
      fieldProps: {
        initialValue: [{}],
        creatorButtonProps: {
          creatorButtonText: "Add Item",
        },
      },
      dataIndex: "items",
      hideInTable: true,
      columns: [
        {
          valueType: "group",

          columns: [
            {
              title: "Item",
              dataIndex: "item",
              type: "dbSelect",
              collection: "parts_and_services",
              label: ["name"],
              isRequired: true,
              colProps: {
                md: 9,
              },
            },
            {
              title: "Quantity",
              dataIndex: "quantity",
              valueType: "digit",
              isRequired: true,
              colProps: {
                md: 5,
              },
            },
            {
              title: "Price",
              dataIndex: "price",
              valueType: "money",
              isRequired: true,
              isPrintable: true,
              colProps: {
                md: 5,
              },
            },
            {
              valueType: "dependency",
              fieldProps: {
                name: ["price", "quantity"],
              },
              columns: ({ price, quantity }) => {
                return [
                  {
                    title: "Sub Total",
                    dataIndex: "subTotal",
                    colProps: {
                      md: 5,
                    },
                    fieldProps: {
                      disabled: true,
                      value: (price ? price : 0) * (quantity ? quantity : 0),
                    },
                  },
                ];
              },
            },
          ],
        },
      ],
    },
    {
      title: "Discounted",
      dataIndex: "discounted",
      valueType: "switch",
      fieldProps: {
        unCheckedChildren: "No",
        checkedChildren: "Yes",
      },
      colProps: {
        md: 2,
        offset: 14,
      },
      hideInTable: true,
    },
    {
      title: "Taxable",
      dataIndex: "taxable",
      valueType: "switch",
      fieldProps: {
        unCheckedChildren: "No",
        checkedChildren: "Yes",
      },
      colProps: {
        md: 2,
      },
      hideInTable: true,
    },
    {
      valueType: "dependency",
      dataIndex: "subTotal",
      title: "Sub Total",
      isPrintable: true,
      fieldProps: {
        name: ["items"],
      },

      render: (record, { items }) => {
        let total = 0;
        items &&
          items.map(
            (item) =>
            (total = item
              ? total +
              (item.price ? item.price : 0) *
              (item.quantity ? item.quantity : 0)
              : total)
          );
        return total.toLocaleString();
      },
      columns: ({ items }) => {
        return [
          {
            title: "Total",
            dataIndex: "total",
            colProps: {
              md: 6,
              // offset: 18,
            },
            fieldProps: {
              disabled: true,
              value: (items || []).reduce(
                (pv, cv) =>
                  pv +
                  (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
                0
              ),
            },
            // valueType: "digit",
          },
        ];
      },
    },

    {
      valueType: "dependency",
      hideInTable: true,
      name: ["taxable", "items", "discounted"],
      columns: ({ taxable, items, discounted }) => {
        let columnsToRender = [];

        if (discounted && items.length > 0) {
          columnsToRender.push({
            dataIndex: "discount_description",
            title: "Discount Description",
            colProps: {
              md: 6,
              offset: !taxable ? 12 : 6,
            },
          });
          columnsToRender.push({
            dataIndex: "discount",
            title: "Discount",
            colProps: {
              md: 6,
              // offset: !taxable ? 18 : 12,
            },
          });
        }

        if (taxable && items.length > 0) {
          columnsToRender.push({
            dataIndex: "tax",
            title: "Tax",
            colProps: {
              md: 6,
              offset: !discounted ? 18 : null,
            },
            fieldProps: {
              disabled: true,
              value:
                items.reduce(
                  (pv, cv) =>
                    pv +
                    (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
                  0
                ) * 0.18,
            },
          });
        }

        return columnsToRender;
      },
    },
    {
      title: "Bill",
      dataIndex: "bill",
      // hideInTable: true,
      sorter: true,
      hideInForm: true,
      isPrintable: true,
      valueType: "money",
      render: (value, record) =>
        (record.bill ? record.bill : 0).toLocaleString(),
    },
    {
      title: "Balance",
      dataIndex: "balance",
      // hideInTable: true,
      sorter: true,
      hideInForm: true,
      valueType: "money",
      render: (value, record) =>
        (record.balance ? record.balance : 0).toLocaleString(),
    },
    {
      valueType: "textarea",
      dataIndex: "description",
      hideInTable: true,
      title: "Description, Remarks or Notes",
    },
  ],
};
