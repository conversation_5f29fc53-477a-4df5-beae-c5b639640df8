import React, { useRef } from "react";
import Invoice from "../CustomViews/Invoice";
import { BetaSchemaForm, TableDropdown } from "@ant-design/pro-components";
import { ScheduleOutlined } from "@ant-design/icons";
import { message } from "antd";
import { LabelFormatter } from "../../../Utils/functions";
import moment from "moment";
import { refreshRecordData } from "../../../Utils/RecordRefreshUtility";

export default {
  CustomView: (data) => {
    const appSettings = JSON.parse(localStorage.getItem("APP_SETTINGS"));
    let bt = appSettings
      ? appSettings.documentHeader && appSettings.documentHeader === "V2"
        ? "Upon approval, Individual Clients are requested to deposit atleast 70% "
        : "Upon approval, Individual Clients are requested to make prior payment "
      : "Upon approval, Individual Clients are requested to make prior payment ";

    return (
      <Invoice
        {...data}
        title="Quotation"
        bottomText=<p>
          {bt}
          OR signed LPO for Companies.
          <br />
          This document is valid till{" "}
          {moment(data.data.date).add(14, "days").format("DD MMM YYYY")}.
        </p>
      />
    );
  },
  MoreActions: (props) => {
    const action = useRef();

    const APP_USER_PERMISSIONS = JSON.parse(
      localStorage.getItem("APP_USER_PERMISSIONS")
    );

    const CanCreateInvoice =
      APP_USER_PERMISSIONS.root ||
      APP_USER_PERMISSIONS.permissions.find((p) => p.module === "invoices")
        ?.create;

    const {
      pouchDatabase,
      collection,
      databasePrefix,
      record,
      CRUD_USER,
      singular,
      modules,
      updateOptimisticRecord,
    } = props;

    // Use the utility function for refreshing record data
    const refreshRecord = async (updatedRecord) => {
      return await refreshRecordData(
        updatedRecord,
        updateOptimisticRecord,
        pouchDatabase,
        collection,
        databasePrefix
      );
    };

    return (
      CanCreateInvoice && (
        <TableDropdown
          key="actionGroup"
          menus={[
            {
              key: "Generate Invoice",
              name: (
                <BetaSchemaForm
                  formRef={action}
                  submitter={{
                    searchConfig: {
                      resetText: "Close",
                      submitText: "Generate Invoice",
                    },
                  }}
                  modalProps={{ centered: true }}
                  grid={true}
                  trigger={
                    <a key="button" type="primary">
                      <ScheduleOutlined /> Generate Invoice
                    </a>
                  }
                  title="Generate Invoice"
                  destroyOnClose={true}
                  layoutType="ModalForm"
                  onFinish={async (values) => {
                    try {
                      // Generate invoice document
                      const invoiceDoc = {
                        ...values,
                        createdAt: new Date().toISOString(),
                        entrant: {
                          value: CRUD_USER._id,
                          label: CRUD_USER.name
                        },
                        quotation: {
                          value: record._id,
                          label: `Quotation #${record._id}`,
                          ...record
                        }
                      };

                      // Save the invoice
                      const invoiceResult = await pouchDatabase(
                        modules.invoices.collection,
                        databasePrefix
                      ).saveDocument(invoiceDoc, CRUD_USER);

                      // If quotation is linked to a job, update job status
                      if (record.job && record.job.value) {
                        const jobDoc = await pouchDatabase(
                          'jobs',
                          databasePrefix
                        ).getDocument(record.job.value);


                        const statusUpdate = {
                          status: "Invoice Generated from Quotation",
                          date: new Date().toISOString(),
                          updatedBy: CRUD_USER,
                          notes: `Invoice #${invoiceResult._id} created from Quotation #${record._id} for amount ${(values.items || []).reduce((sum, item) => sum + (item.price * item.quantity), 0)
                            }`
                        };




                        await pouchDatabase(
                          'jobs',
                          databasePrefix
                        ).saveDocument({
                          ...jobDoc,
                          job_status: [...(jobDoc.job_status || []), statusUpdate]
                        }, CRUD_USER);
                      }

                      // Update quotation record to mark as invoiced
                      const updatedQuotation = {
                        ...record,
                        invoiced: true,
                        invoice_id: invoiceResult._id,
                        invoiced_at: new Date().toISOString()
                      };

                      await pouchDatabase(collection, databasePrefix).saveDocument(updatedQuotation, CRUD_USER);

                      // Refresh quotation record for immediate UI updates
                      await refreshRecord(updatedQuotation);

                      message.success(`Invoice generated successfully`);
                      return true;
                    } catch (error) {
                      console.error('Error generating invoice:', error);
                      message.error('Failed to generate invoice');
                      return false;
                    }
                  }}
                  columns={[...modules.invoices.columns]}
                  initialValues={{
                    job: record.job,
                    customer: record.customer,
                    items: record.items,
                    date: new Date().toISOString(),
                    taxable: record.taxable,
                    discount: record.discount,
                    discountable: record.discountable,
                    notes: `Generated from Quotation #${record._id}`
                  }}
                />
              ),
            },
          ]}
        />
      )
    );
  },
};
