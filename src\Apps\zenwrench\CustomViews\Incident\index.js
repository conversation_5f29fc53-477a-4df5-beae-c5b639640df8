import React, { useEffect, useState } from "react";
import "./css.css";
import {
  Descriptions,
  Divider,
  Layout,
  Typography,
  Button,
  Flex,
  FloatButton,
  Card,
  Row,
  Col,
  Timeline,
  Tag,
  Avatar,
  Alert,
} from "antd";
import {
  AlertOutlined,
  PrinterOutlined,
  FileTextOutlined,
  ClockCircleOutlined,
  UserOutlined,
  EnvironmentOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import moment from "moment";
import PrintComponents from "react-print-components";
import RenderBlob from "../../../../Components/RenderBlob";
import { PageHeader } from "@ant-design/pro-components";

const { Content } = Layout;
const { Title, Text, Paragraph } = Typography;

const Incident = (props) => {
  const { data, pouchDatabase, databasePrefix, ...rest } = props;
  const [company, setCompany] = useState(null);
  const [reporter, setReporter] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch related data
  useEffect(() => {
    const fetchData = async () => {
      if (!pouchDatabase || databasePrefix === undefined) {
        setLoading(false);
        return;
      }

      try {
        // Fetch company data
        const companyData = await pouchDatabase("organizations", databasePrefix).getAllData();
        if (companyData && companyData.length > 0) {
          setCompany(companyData[0]);
        }

        // In a real application, you would fetch reporter data here
        // For now, we'll create some mock data
        const mockReporter = {
          _id: "user123",
          first_name: "John",
          last_name: "Doe",
          position: "Mechanic",
          email: "<EMAIL>",
          phone: "+1234567890",
        };

        setReporter(mockReporter);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        setLoading(false);
      }
    };

    fetchData();
  }, [data, pouchDatabase, databasePrefix]);

  // Get severity level
  const getSeverityLevel = () => {
    // In a real application, this would be based on actual data
    // For now, we'll randomly assign a severity level
    const levels = ["low", "medium", "high", "critical"];
    const randomIndex = Math.floor(Math.random() * levels.length);
    return levels[randomIndex];
  };

  // Get severity color
  const getSeverityColor = (severity) => {
    switch (severity) {
      case "low":
        return "green";
      case "medium":
        return "blue";
      case "high":
        return "orange";
      case "critical":
        return "red";
      default:
        return "default";
    }
  };

  // Get incident status
  const getIncidentStatus = () => {
    // In a real application, this would be based on actual data
    // For now, we'll randomly assign a status
    const statuses = ["open", "investigating", "resolved", "closed"];
    const randomIndex = Math.floor(Math.random() * statuses.length);
    return statuses[randomIndex];
  };

  // Get status color
  const getStatusColor = (status) => {
    switch (status) {
      case "open":
        return "red";
      case "investigating":
        return "orange";
      case "resolved":
        return "green";
      case "closed":
        return "blue";
      default:
        return "default";
    }
  };

  // Mock incident timeline
  const getIncidentTimeline = () => {
    const createdDate = moment().subtract(3, 'days');
    const updatedDate = moment().subtract(2, 'days');
    const resolvedDate = moment().subtract(1, 'day');

    return [
      {
        color: "red",
        children: (
          <>
            <Text strong>Incident Reported</Text>
            <br />
            <Text type="secondary">
              {createdDate.format("DD MMM YYYY, HH:mm")}
            </Text>
            <br />
            <Text>Initial report filed by {reporter?.first_name} {reporter?.last_name}</Text>
          </>
        ),
      },
      {
        color: "orange",
        children: (
          <>
            <Text strong>Investigation Started</Text>
            <br />
            <Text type="secondary">
              {updatedDate.format("DD MMM YYYY, HH:mm")}
            </Text>
            <br />
            <Text>Safety team assigned to investigate the incident</Text>
          </>
        ),
      },
      {
        color: "green",
        children: (
          <>
            <Text strong>Incident Resolved</Text>
            <br />
            <Text type="secondary">
              {resolvedDate.format("DD MMM YYYY, HH:mm")}
            </Text>
            <br />
            <Text>Corrective actions implemented</Text>
          </>
        ),
      },
    ];
  };

  // Printable incident component
  const PrintableIncident = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            <RenderBlob blob={company.logo} size={150} />
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>INCIDENT REPORT</Title>
            <Text>Incident: {data.name}</Text>
            <br />
            <Text>ID: {data._id}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={4}>Incident Information</Title>
      <Descriptions column={2} bordered>
        <Descriptions.Item label="Incident Name">{data.name}</Descriptions.Item>
        <Descriptions.Item label="Date Reported">
          {moment().subtract(3, 'days').format("DD MMM YYYY")}
        </Descriptions.Item>
        <Descriptions.Item label="Severity">
          <Tag color={getSeverityColor(getSeverityLevel())}>
            {getSeverityLevel().toUpperCase()}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="Status">
          <Tag color={getStatusColor(getIncidentStatus())}>
            {getIncidentStatus().toUpperCase()}
          </Tag>
        </Descriptions.Item>
        <Descriptions.Item label="Description" span={2}>
          {data.description || "No description provided"}
        </Descriptions.Item>
      </Descriptions>

      {reporter && (
        <>
          <Divider />
          <Title level={4}>Reporter Information</Title>
          <Descriptions column={2} bordered>
            <Descriptions.Item label="Name">
              {reporter.first_name} {reporter.last_name}
            </Descriptions.Item>
            <Descriptions.Item label="Position">
              {reporter.position}
            </Descriptions.Item>
            <Descriptions.Item label="Email">
              {reporter.email}
            </Descriptions.Item>
            <Descriptions.Item label="Phone">
              {reporter.phone}
            </Descriptions.Item>
          </Descriptions>
        </>
      )}

      <Divider />

      <Title level={4}>Incident Timeline</Title>
      <div className="incident-timeline-print">
        {getIncidentTimeline().map((item, index) => (
          <div key={index} className="timeline-item">
            <div className={`timeline-dot ${item.color}`}></div>
            <div className="timeline-content">
              {item.children}
            </div>
          </div>
        ))}
      </div>

      <Divider />

      <div style={{ marginTop: "50px" }}>
        <div style={{ display: "flex", justifyContent: "space-between" }}>
          <div>
            <p>Prepared By: _________________________</p>
          </div>
          <div>
            <p>Approved By: _________________________</p>
          </div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return <div>Loading incident information...</div>;
  }

  const severity = getSeverityLevel();
  const status = getIncidentStatus();

  return (
    <>
      <PageHeader
        backIcon={<AlertOutlined style={{ fontSize: 30 }} />}
        onBack={() => window.history.back()}
        title={data.name}
        subTitle="Incident Report"
        tags={[
          <Tag color={getSeverityColor(severity)} key="severity">
            Severity: {severity.toUpperCase()}
          </Tag>,
          <Tag color={getStatusColor(status)} key="status">
            Status: {status.toUpperCase()}
          </Tag>
        ]}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Report
              </Button>
            }
          >
            <PrintableIncident />
          </PrintComponents>,
        ]}
      />

      <Content style={{ padding: "0 24px 24px" }}>
        {status === "open" && (
          <Alert
            message="Active Incident"
            description="This incident is currently open and requires attention."
            type="error"
            showIcon
            icon={<ExclamationCircleOutlined />}
            style={{ marginBottom: 24 }}
          />
        )}

        {status === "investigating" && (
          <Alert
            message="Under Investigation"
            description="This incident is currently being investigated."
            type="warning"
            showIcon
            icon={<ExclamationCircleOutlined />}
            style={{ marginBottom: 24 }}
          />
        )}

        <Row gutter={[24, 24]}>
          <Col xs={24} lg={16}>
            <Card title={<><FileTextOutlined /> Incident Details</>} className="incident-card">
              <Descriptions bordered column={{ xxl: 2, xl: 2, lg: 2, md: 1, sm: 1, xs: 1 }}>
                <Descriptions.Item label="Incident Name">{data.name}</Descriptions.Item>
                <Descriptions.Item label="Date Reported">
                  {moment().subtract(3, 'days').format("DD MMM YYYY")}
                </Descriptions.Item>
                <Descriptions.Item label="Location">Workshop Floor</Descriptions.Item>
                <Descriptions.Item label="Department">Maintenance</Descriptions.Item>
                <Descriptions.Item label="Description" span={2}>
                  {data.description || "No description provided"}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card title={<><UserOutlined /> Reporter Information</>} className="incident-card">
              {reporter && (
                <Flex align="center" gap="middle" style={{ marginBottom: 16 }}>
                  <Avatar size={64} icon={<UserOutlined />} />
                  <div>
                    <Text strong style={{ fontSize: 16 }}>
                      {reporter.first_name} {reporter.last_name}
                    </Text>
                    <br />
                    <Text type="secondary">{reporter.position}</Text>
                    <br />
                    <Text>{reporter.email}</Text>
                  </div>
                </Flex>
              )}
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col xs={24}>
            <Card title={<><ClockCircleOutlined /> Incident Timeline</>} className="incident-card">
              <Timeline
                mode="left"
                items={getIncidentTimeline()}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col xs={24}>
            <Card title={<><EnvironmentOutlined /> Incident Location</>} className="incident-card">
              <div className="incident-location">
                <div className="location-placeholder">
                  <EnvironmentOutlined style={{ fontSize: 64, color: '#bfbfbf' }} />
                  <Text type="secondary" style={{ marginTop: 16 }}>Location map not available</Text>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
      </Content>

      <FloatButton
        icon={<PrinterOutlined />}
        type="primary"
        tooltip="Print Report"
        onClick={() => document.getElementById("print-button").click()}
        style={{ right: 94 }}
      />

      {/* Hidden print button for FloatButton to trigger */}
      <div style={{ display: "none" }}>
        <PrintComponents
          trigger={<Button id="print-button">Print</Button>}
        >
          <PrintableIncident />
        </PrintComponents>
      </div>
    </>
  );
};

export default Incident;
