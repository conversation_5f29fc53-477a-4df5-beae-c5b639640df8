import React, { useEffect, useState } from "react";
import {
  Card,
  Row,
  Col,
  Tag,
  Typography,
  Space,
  FloatButton,
  Flex,
  Divider,
} from "antd";
import {
  CarOutlined,
  ClockCircleOutlined,
  UserOutlined,
  PrinterOutlined,
} from "@ant-design/icons";
import PrintComponents from "react-print-components";
import RenderBlob from "../../../../Components/RenderBlob";
import moment from "moment";
import "./styles.css";

const { Title, Text } = Typography;

const GatePass = (props) => {
  const [company, setCompany] = useState(null);
  const [customer, setCustomer] = useState(null);
  const [vehicle, setVehicle] = useState(null);
  const record = props.data;
  const { pouchDatabase, databasePrefix } = props;

  useEffect(() => {
    if (pouchDatabase && databasePrefix !== undefined) {
      pouchDatabase("organizations", databasePrefix)
        .getAllData()
        .then((docs) => {
          if (docs.length > 0) {
            // Note: ElectronDB doesn't support attachments the same way as PouchDB
            // You may need to handle logo differently or store it as base64
            setCompany(docs[0]);
          }
        })
        .catch((error) => {
          console.error("Error fetching organizations:", error);
        });
    }
  }, [pouchDatabase, databasePrefix]);

  useEffect(() => {
    if (record?.job?.value && pouchDatabase && databasePrefix !== undefined) {
      pouchDatabase("jobs", databasePrefix)
        .getDocument(record.job.value)
        .then((jobData) => {
          if (jobData) {
            // Fetch customer data
            if (jobData.customer?.value || jobData.customer?._id) {
              const customerId = jobData.customer.value || jobData.customer._id;
              pouchDatabase("customers", databasePrefix)
                .getDocument(customerId)
                .then((customerData) => {
                  setCustomer(customerData);
                })
                .catch((error) =>
                  console.error("Error fetching customer:", error),
                );
            }

            // Fetch vehicle data
            if (jobData.vehicle?.value) {
              pouchDatabase("vehicles", databasePrefix)
                .getDocument(jobData.vehicle.value)
                .then((vehicleData) => {
                  setVehicle(vehicleData);
                })
                .catch((error) =>
                  console.error("Error fetching vehicle:", error),
                );
            }
          }
        })
        .catch((error) => console.error("Error fetching job:", error));
    }
  }, [record?.job?.value, pouchDatabase, databasePrefix]);

  const { job, type, date, description } = record || {};

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case "active":
        return "green";
      case "completed":
        return "blue";
      case "expired":
        return "red";
      default:
        return "default";
    }
  };

  const toSentenceCase = (str) => {
    if (!str) return "";
    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
  };

  return (
    <div className="tm_container">
      <div className="tm_invoice_wrap">
        <div className="tm_invoice tm_style1">
          <div className="tm_invoice_in">
            {/* Header */}
            <div className="tm_invoice_head tm_mb20">
              <div className="tm_invoice_left">
                <div className="tm_logo">
                  {company?.logo && (
                    <RenderBlob blob={company.logo} size={200} />
                  )}
                </div>
              </div>
              <div className="tm_invoice_right tm_text_right">
                <div className="tm_primary_color tm_f14 tm_text_uppercase">
                  {company?.name}
                </div>
                <span className="tm_text tm_f11">{company?.address}</span>
                <br />
                <div className="tm_text  tm_f11">
                  {company?.phone}{" "}
                  {company && company.alternative_phone
                    ? `| ${company.alternative_phone}`
                    : ""}
                </div>
                <div className="tm_text  tm_f11">{company?.email}</div>
              </div>
            </div>
            <Divider />
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Space direction="horizontal" size="middle">
                  <Title style={{ marginBottom: 0 }} level={4}>
                    Gate Pass #{record?._id}
                  </Title>
                  <Tag color={getStatusColor(type)}>{toSentenceCase(type)}</Tag>
                </Space>
              </Col>

              {vehicle && <Col xs={24} sm={12}>
                <Card size="small" title="Vehicle Information">
                  <Space direction="vertical">
                    <Space>
                      <CarOutlined />
                      <Text strong>Vehicle:</Text>
                      <Text>{vehicle?._id}</Text>
                    </Space>
                    <Space>
                      <Text strong>Make:</Text>
                      <Text>{`${vehicle?.make} ${vehicle?.modal} ${vehicle.year ? vehicle.year : ""}`}</Text>
                    </Space>
                    <Space>
                      <Text strong>Number Plate:</Text>
                      <Text>{vehicle?.reg_number}</Text>
                    </Space>
                  </Space>
                </Card>
              </Col>}

              {customer && <Col xs={24} sm={12}>
                <Card size="small" title="Customer Information">
                  <Space direction="vertical">
                    <Space>
                      <UserOutlined />
                      <Text strong>Name:</Text>
                      <Text>{customer?.name}</Text>
                    </Space>
                    <Space>
                      <Text strong>Phone:</Text>
                      <Text>{customer?.phone}</Text>
                    </Space>
                    <Space>
                      <Text strong>Email:</Text>
                      <Text>{customer?.email}</Text>
                    </Space>
                  </Space>
                </Card>
              </Col>}

              <Col span={24}>
                <Card size="small" title="Gate Pass Details">
                  <Row gutter={[16, 8]}>
                    <Col span={8}>
                      <Text strong>Date: </Text>
                      <Text>{moment(date).format("DD MMM YY - HH:mm:ss")}</Text>
                    </Col>
                    <Col span={8}>
                      <Text strong>Type: </Text>
                      <Text>{toSentenceCase(type)}</Text>
                    </Col>
                    <Col span={8}>
                      <Text strong>Job Reference: </Text>
                      <Text>{record?.job?.value}</Text>
                    </Col>
                    <Col span={24}>
                      <Text strong>Description: </Text>
                      <Text>{description}</Text>
                    </Col>
                  </Row>
                </Card>
              </Col>

              {/* Signatures Section */}
              <Col span={24}>
                <div style={{ marginTop: 40 }}>
                  <Row gutter={[48, 24]} justify="space-between">
                    <Col xs={24} sm={12}>
                      <div className="signature-section">
                        <div className="signature-line"></div>
                        <Text strong>Manager / Supervisor</Text>
                        <div style={{ marginTop: 8 }}>
                          <Text type="secondary">Name & Signature</Text>
                        </div>
                      </div>
                    </Col>
                    <Col xs={24} sm={12}>
                      <div className="signature-section">
                        <div className="signature-line"></div>
                        <Text strong>
                          {type?.toLowerCase() === "release"
                            ? "Customer/Driver"
                            : "Financial Officer"}
                        </Text>
                        <div style={{ marginTop: 8 }}>
                          <Text type="secondary">Name & Signature</Text>
                        </div>
                      </div>
                    </Col>
                  </Row>
                </div>
              </Col>
            </Row>
          </div>
        </div>
      </div>
    </div>
  );
};

const PrintableGatePass = (props) => {
  return (
    <>
      <PrintComponents
        trigger={
          <FloatButton icon={<PrinterOutlined />} tooltip={<div>Print</div>} />
        }
      >
        <GatePass {...props} />
      </PrintComponents>
      <GatePass {...props} />
    </>
  );
};

export default PrintableGatePass;
