import React, { useEffect, useState } from 'react';
import './css.css';
import {
  Card,
  Rate,
  Typography,
  Row,
  Col,
  Descriptions,
  Tag,
  Button,
  Flex,
  FloatButton,
  Timeline,
  Avatar,
  Divider,
  Layout,
  Statistic,
  Space
} from 'antd';
import {
  CommentOutlined,
  UserOutlined,
  CalendarOutlined,
  PrinterOutlined,
  StarOutlined,
  PhoneOutlined,
  MailOutlined,
  SmileOutlined,
  MehOutlined,
  FrownOutlined
} from '@ant-design/icons';
import moment from 'moment';
import PrintComponents from 'react-print-components';
import RenderBlob from '../../../../Components/RenderBlob';
import { PageHeader } from '@ant-design/pro-components';

const { Content } = Layout;
const { Text, Title, Paragraph } = Typography;

const Feedback = (props) => {
  const { data, pouchDatabase, databasePrefix, ...rest } = props;
  const [customer, setCustomer] = useState(null);
  const [contactedBy, setContactedBy] = useState(null);
  const [company, setCompany] = useState(null);
  const [loading, setLoading] = useState(true);

  // Fetch related data
  useEffect(() => {
    const fetchData = async () => {
      if (!pouchDatabase || databasePrefix === undefined) {
        setLoading(false);
        return;
      }

      try {
        // Fetch customer data
        if (data.customer && data.customer.value) {
          const customerData = await pouchDatabase("customers", databasePrefix).getDocument(data.customer.value);
          setCustomer(customerData);
        }

        // Fetch contacted_by user data
        if (data.contacted_by && data.contacted_by.value) {
          const userData = await pouchDatabase("users", databasePrefix).getDocument(data.contacted_by.value);
          setContactedBy(userData);
        }

        // Fetch company data
        const companyData = await pouchDatabase("organizations", databasePrefix).getAllData();
        if (companyData && companyData.length > 0) {
          setCompany(companyData[0]);
        }

        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        setLoading(false);
      }
    };

    fetchData();
  }, [data, pouchDatabase, databasePrefix]);

  const getRatingColor = (rating) => {
    if (rating >= 4) return '#52c41a'; // green
    if (rating >= 3) return '#faad14'; // yellow
    return '#f5222d'; // red
  };

  const getRatingIcon = (rating) => {
    if (rating >= 4) return <SmileOutlined />;
    if (rating >= 3) return <MehOutlined />;
    return <FrownOutlined />;
  };

  const getRatingText = (rating) => {
    if (rating >= 4.5) return "Excellent";
    if (rating >= 4) return "Very Good";
    if (rating >= 3) return "Good";
    if (rating >= 2) return "Poor";
    return "Very Poor";
  };

  // Printable feedback component
  const PrintableFeedback = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            <RenderBlob blob={company.logo} size={150} />
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>CUSTOMER FEEDBACK</Title>
            <Text>Feedback ID: {data._id}</Text>
            <br />
            <Text>Date: {moment(data.date).format("DD MMM YYYY")}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={4}>Customer Information</Title>
      {customer && (
        <Descriptions column={2} bordered>
          <Descriptions.Item label="Name">{customer.name}</Descriptions.Item>
          <Descriptions.Item label="Phone">{customer.phone}</Descriptions.Item>
          {customer.email && (
            <Descriptions.Item label="Email">{customer.email}</Descriptions.Item>
          )}
          {customer.address && (
            <Descriptions.Item label="Address">{customer.address}</Descriptions.Item>
          )}
        </Descriptions>
      )}

      <Divider />

      <Title level={4}>Feedback Details</Title>
      <Descriptions column={1} bordered>
        <Descriptions.Item label="Rating">
          <Rate
            disabled
            value={data.rating}
            allowHalf
            style={{ color: getRatingColor(data.rating) }}
          />
          <Text style={{ marginLeft: 16, color: getRatingColor(data.rating) }}>
            {data.rating}/5 - {getRatingText(data.rating)}
          </Text>
        </Descriptions.Item>
        <Descriptions.Item label="Feedback">
          {data.feedback}
        </Descriptions.Item>
        <Descriptions.Item label="Contacted By">
          {contactedBy ? `${contactedBy.first_name} ${contactedBy.last_name}` : data.contacted_by?.label || "N/A"}
        </Descriptions.Item>
        <Descriptions.Item label="Date">
          {moment(data.date).format("DD MMM YYYY, HH:mm")}
        </Descriptions.Item>
      </Descriptions>

      <Divider />

      <div style={{ marginTop: "50px", textAlign: "center" }}>
        <p>Generated on: {moment().format("DD MMM YYYY, HH:mm")}</p>
      </div>
    </div>
  );

  if (loading) {
    return <div>Loading feedback information...</div>;
  }

  return (
    <>
      <PageHeader
        backIcon={<CommentOutlined style={{ fontSize: 30 }} />}
        onBack={() => window.history.back()}
        title="Customer Feedback"
        subTitle={`Feedback ID: ${data._id}`}
        tags={
          <Tag color={getRatingColor(data.rating)}>
            {getRatingText(data.rating)}
          </Tag>
        }
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Feedback
              </Button>
            }
          >
            <PrintableFeedback />
          </PrintComponents>,
        ]}
      />

      <Content style={{ padding: "0 24px 24px" }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} md={8}>
            <Card className="feedback-card">
              <Statistic
                title="Rating"
                value={data.rating}
                precision={1}
                valueStyle={{ color: getRatingColor(data.rating) }}
                prefix={getRatingIcon(data.rating)}
                suffix="/ 5"
              />
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card className="feedback-card">
              <Statistic
                title="Date"
                value={moment(data.date).format("DD MMM YYYY")}
                valueStyle={{ color: '#1890ff' }}
                prefix={<CalendarOutlined />}
              />
            </Card>
          </Col>
          <Col xs={24} md={8}>
            <Card className="feedback-card">
              <Statistic
                title="Status"
                value={getRatingText(data.rating)}
                valueStyle={{ color: getRatingColor(data.rating) }}
                prefix={<StarOutlined />}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col xs={24} lg={12}>
            <Card
              title={<><UserOutlined /> Customer Information</>}
              className="feedback-card"
            >
              {customer ? (
                <Descriptions column={1} bordered>
                  <Descriptions.Item label="Name">{customer.name}</Descriptions.Item>
                  <Descriptions.Item label={<><PhoneOutlined /> Phone</>}>
                    {customer.phone}
                  </Descriptions.Item>
                  {customer.email && (
                    <Descriptions.Item label={<><MailOutlined /> Email</>}>
                      {customer.email}
                    </Descriptions.Item>
                  )}
                  {customer.address && (
                    <Descriptions.Item label="Address">{customer.address}</Descriptions.Item>
                  )}
                </Descriptions>
              ) : (
                <Text type="secondary">
                  {data.customer?.label ? `Customer: ${data.customer.label}` : "No customer information available"}
                </Text>
              )}
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card
              title={<><CommentOutlined /> Feedback</>}
              className="feedback-card"
            >
              <div className="feedback-rating">
                <Rate
                  disabled
                  value={data.rating}
                  allowHalf
                  style={{ color: getRatingColor(data.rating), fontSize: 24 }}
                />
                <Text style={{ marginLeft: 16, color: getRatingColor(data.rating), fontSize: 16 }}>
                  {data.rating}/5 - {getRatingText(data.rating)}
                </Text>
              </div>
              <Divider style={{ margin: '16px 0' }} />
              <div className="feedback-content">
                <Paragraph>
                  {data.feedback || "No feedback provided"}
                </Paragraph>
              </div>
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col xs={24}>
            <Card
              title={<><CalendarOutlined /> Feedback Timeline</>}
              className="feedback-card"
            >
              <Timeline
                mode="left"
                items={[
                  {
                    color: 'blue',
                    children: (
                      <>
                        <Text strong>Feedback Received</Text>
                        <br />
                        <Text type="secondary">
                          {moment(data.date).format("DD MMM YYYY, HH:mm")}
                        </Text>
                        <br />
                        <Text>Customer provided feedback with rating: {data.rating}/5</Text>
                      </>
                    ),
                  },
                  {
                    color: 'green',
                    children: (
                      <>
                        <Text strong>Feedback Recorded</Text>
                        <br />
                        <Text type="secondary">
                          {moment(data.date).format("DD MMM YYYY, HH:mm")}
                        </Text>
                        <br />
                        <Text>
                          Recorded by: {contactedBy ? `${contactedBy.first_name} ${contactedBy.last_name}` : data.contacted_by?.label || "System"}
                        </Text>
                      </>
                    ),
                  },
                ]}
              />
            </Card>
          </Col>
        </Row>
      </Content>

      <FloatButton
        icon={<PrinterOutlined />}
        type="primary"
        tooltip="Print Feedback"
        onClick={() => document.getElementById("print-button").click()}
        style={{ right: 94 }}
      />

      {/* Hidden print button for FloatButton to trigger */}
      <div style={{ display: "none" }}>
        <PrintComponents
          trigger={<Button id="print-button">Print</Button>}
        >
          <PrintableFeedback />
        </PrintComponents>
      </div>
    </>
  );
};

export default Feedback;