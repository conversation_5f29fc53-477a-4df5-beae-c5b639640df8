import React, { useEffect, useState } from "react";
import { Flex, Divider, Image, Space } from "antd";
import RenderBlob from "../../../../Components/RenderBlob";
import { toWordsUGX } from "../../../../Utils/functions";
import moment from "moment";
import DocumentFooter from "../DocumentFooter";
import { LOCAL_STORAGE_ORGANIZATION } from "../../../../Utils/constants";
import { safeFilterReceiptsByInvoice, safeCalculateReceiptTotal } from "../../../../Utils/ReceiptDataUtils";

const nagoaLogo = require("../Invoice/nagoaLogo.png");

function ChildReceipt(props) {
  const { data, copy, pouchDatabase, databasePrefix } = props;

  const gridContainerStyle = {
    display: "grid",
    gridTemplateColumns: "auto 1fr",
    gap: "1px", // Optional, for spacing between columns
    fontSize: 14,
    paddingTop: 20,
  };

  const contentStyle = {
    padding: "1px",
  };

  const PaymentMethodText = props.columns.find((x) => x.dataIndex === "pMethod")
    ?.valueEnum[data.pMethod].text;

  const appSettings = JSON.parse(localStorage.getItem("APP_SETTINGS"));

  
  const [company, setCompany] = useState(null);
  const [invoceTotal, setInvoiceTotal] = useState(0);
  const [invoceTotalPaid, setInvoiceTotalPaid] = useState(0);
  const [job, setJob] = useState(null);
  useEffect(() => {
    if (pouchDatabase && databasePrefix !== undefined) {
      // Get organization ID from localStorage for better performance
      const storedOrganization = JSON.parse(localStorage.getItem(LOCAL_STORAGE_ORGANIZATION));

      if (storedOrganization && storedOrganization._id) {
        // Use getDocument with specific organization ID
        pouchDatabase("organizations", databasePrefix)
          .getDocument(storedOrganization._id)
          .then((comp) => {
            if (comp) {
              // Note: ElectronDB doesn't support attachments the same way as PouchDB
              // You may need to handle logo differently or store it as base64
              setCompany(comp);
            }
          })
          .catch((error) => {
            console.error("Error fetching organization:", error);
            // Fallback to getting all organizations
            pouchDatabase("organizations", databasePrefix)
              .getAllData()
              .then((docs) => {
                if (docs.length > 0) {
                  setCompany(docs[0]);
                }
              })
              .catch((fallbackError) => {
                console.error("Error fetching organizations fallback:", fallbackError);
              });
          });
      } else {
        // Fallback to getting all organizations if no organization in localStorage
        pouchDatabase("organizations", databasePrefix)
          .getAllData()
          .then((docs) => {
            if (docs.length > 0) {
              setCompany(docs[0]);
            }
          })
          .catch((error) => {
            console.error("Error fetching organizations:", error);
          });
      }
    }
  }, [pouchDatabase, databasePrefix]);

  useEffect(() => {
    if (data.invoice?.value && pouchDatabase && databasePrefix !== undefined) {
      pouchDatabase("invoices", databasePrefix)
        .getDocument(data.invoice.value)
        .then((res) => {
          if (res) {
            const discount = res.discount ? res.discount : 0;
            const subTotal = res.items.reduce(
              (acc, item) => acc + item.price * item.quantity,
              0
            );
            const tax = res.taxable ? (subTotal - discount) * 0.18 : 0;

            setInvoiceTotal(subTotal - discount + tax);
          }
        })
        .catch((error) => {
          console.error("Error fetching invoice:", error);
        });
    }
  }, [data.invoice?.value, pouchDatabase, databasePrefix]);

  useEffect(() => {
    if (data.invoice?.value && pouchDatabase && databasePrefix !== undefined) {
      pouchDatabase("invoices", databasePrefix)
        .getDocument(data.invoice.value)
        .then((res) => {
          if (res) {
            setJob(res.job);
          }
        })
        .catch((error) => {
          console.error("Error fetching invoice for job:", error);
        });
    }
  }, [data.invoice?.value, pouchDatabase, databasePrefix]);

  useEffect(() => {
    if (data.invoice && pouchDatabase && databasePrefix !== undefined) {
      pouchDatabase("receipts", databasePrefix)
        .getAllData()
        .then((docs) => {
          // Convert to the expected format with rows structure for the utility function
          const rows = docs.map(doc => ({ doc }));

          // Use safe utility function to filter receipts by invoice
          const filteredData = safeFilterReceiptsByInvoice(
            rows,
            data.invoice.value,
            data.date
          );

          // Use safe utility function to calculate total
          const totalPaid = safeCalculateReceiptTotal(filteredData);
          setInvoiceTotalPaid(totalPaid);
        })
        .catch((error) => {
          console.error("Error fetching receipts:", error);
          setInvoiceTotalPaid(0);
        });
    } else {
      setInvoiceTotalPaid(0);
    }
  }, [data.invoice?.value, data.date, pouchDatabase, databasePrefix]);

  

  return !company ? null : (
    <div>
      <Flex style={{ width: "100%" }} align="center" justify="space-between">
        <div style={{ width: 250, fontSize: 12 }}>
          <RenderBlob blob={company.logo} size={250} />
          <br />
          <br />
          <p>
            {company.address}
            <br />
            Tel: {company.phone} / {company.alternative_phone} <br />
            Email: {company.email} <br />
            {company.website && company.website}
          </p>
        </div>
        {/* <Divider> */}
        <strong style={{ fontSize: 30 }}>RECEIPT</strong>
        {/* </Divider> */}
        <div style={{ width: 250, fontSize: 12 }}>
          <div style={{ marginLeft: 20, textAlign: "right" }}>
            <p>
              Receipt No : {data._id}
              <br />
              Receipt Date : {moment(data.date).format("DD-MMM-YY")}
              <br />
              <br />
              Printed On : {moment().format("d DD-MMM-YY HH:mm:a")}
            </p>
          </div>
        </div>
      </Flex>
      {/* <Divider orientation="right" style={{ marginTop: 0 }}>
        {copy} Copy
      </Divider> */}
      <div style={gridContainerStyle}>
        <div style={contentStyle}>
          {/* First column content */}
          Received With Thanks From :{" "}
        </div>
        <div
          style={
            (contentStyle,
              { borderBottom: "1px dotted black", fontWeight: "bold" })
          }
        >
          {/* Second column content */}
          {data.invoice
            ? data.invoice.label.split(" - ")[0]
            : data.customer.label}
        </div>
      </div>
      <div style={gridContainerStyle}>
        <div style={contentStyle}>
          {/* First column content */}
          The sum of :{" "}
        </div>
        <div
          style={
            (contentStyle,
              { borderBottom: "1px dotted black", fontWeight: "bold" })
          }
        >
          {/* Second column content */}
          {toWordsUGX.convert(data.amount)}
        </div>
      </div>
      <div style={gridContainerStyle}>
        <div style={contentStyle}>
          {/* First column content */}
          Towards Payment for{" "}
          {job
            ? job.job_type === "wheel_alignment"
              ? "Wheel Alignment done on "
              : job.job_type === "wheel_balance"
                ? "Wheel Balance done on "
                : job.job_type === "wasing_bay"
                  ? "Car Wash done on "
                  : job.job_type === "diagnosis"
                    ? "Diagnosis on "
                    : "work done on the"
            : " "}
          :{"  "}
        </div>
        <div
          style={
            (contentStyle,
              { borderBottom: "1px dotted black", fontWeight: "bold" })
          }
        >
          {/* Second column content */}
          {data.invoice && data.invoice.label.split(" - ")[1]}{" "}
          {data.invoice && data.invoice.label.split(" - ")[2].split(" [")[0]}
          {!data.invoice && data.description}
        </div>
      </div>
      {data.invoice && (
        <div style={gridContainerStyle}>
          <div style={contentStyle}>
            {/* First column content */}
            Invoice Number :{" "}
          </div>
          <div
            style={
              (contentStyle,
                { borderBottom: "1px dotted black", fontWeight: "bold" })
            }
          >
            {/* Second column content */}
            {data.invoice.value}
          </div>
        </div>
      )}
      <Flex
        style={{ width: "100%", padding: 20 }}
        align="center"
        justify="space-between"
      >
        <div>
          Payment Method : <strong>{PaymentMethodText}</strong>
        </div>
        <div style={{ textAlign: "right" }}>
          <table style={{ fontSize: 18, fontWeight: "bold" }}>
            {invoceTotal > 0 && (
              <tr>
                <td style={{ paddingBottom: 0 }}>Amount Payable :</td>
                <td style={{ paddingBottom: 0 }}>
                  UGX {(invoceTotal - invoceTotalPaid).toLocaleString()}
                </td>
              </tr>
            )}
            <tr>
              <td style={{ paddingBottom: 0 }}>Amount Received :</td>
              <td style={{ paddingBottom: 0 }}>
                UGX {data.amount.toLocaleString()}
              </td>
            </tr>
            {invoceTotal > 0 && data.withholding && (
              <tr>
                <td>Withholding Tax :</td>
                <td>
                  UGX{" "}
                  {(invoceTotal - invoceTotalPaid - data.amount > 0
                    ? invoceTotal - invoceTotalPaid - data.amount
                    : 0
                  ).toLocaleString()}
                </td>
              </tr>
            )}
            {invoceTotal > 0 && !data.withholding && (
              <tr>
                <td>Balance :</td>
                <td>
                  UGX{" "}
                  {(invoceTotal - invoceTotalPaid - data.amount > 0
                    ? invoceTotal - invoceTotalPaid - data.amount
                    : 0
                  ).toLocaleString()}
                </td>
              </tr>
            )}
          </table>
        </div>
      </Flex>

      <Flex
        style={{ width: "100%", padding: 20 }}
        align="center"
        justify="space-between"
      >
        <center>
          <br />
          .............................................
          <br />
          For {company.name}
        </center>
        <center>
          No refunds will be issued for this transaction.
        </center>
        <center>
          <br />
          .............................................
          <br />
          Customer Signature
        </center>
      </Flex>

      <DocumentFooter />
      {/* <Divider orientation="left">Disclaimer: No refunds will be issued for this transaction.</Divider> */}
      {/* <Divider orientation="left">Please note that all sales are final and no refunds or exchanges will be accepted.</Divider> */}
    </div>
  );
}

const Receipt = (props) => (
  <>
    <ChildReceipt {...props} copy="Customer" />
    {/* <Divider style={{ marginTop: 50, marginBottom: 30 }} />
    <ChildReceipt {...props} copy="Company" /> */}
  </>
);

export default Receipt;
