import React, { useEffect, useState } from "react";
import "./css.css";
import RenderBlob from "../../../../Components/RenderBlob";
import moment from "moment";
import PrintComponents from "react-print-components";
import { Float<PERSON><PERSON><PERSON>, Flex, Divider } from "antd";
import { PrinterOutlined } from "@ant-design/icons";
import { numberFormat } from "../../../../Utils/functions";
import DocumentFooter from "../DocumentFooter";
import { LOCAL_STORAGE_ORGANIZATION } from "../../../../Utils/constants";

const Quotation = (props) => {
  const { data, title, bottomText = "" } = props;

  

  const appSettings = JSON.parse(localStorage.getItem("APP_SETTINGS")) || {};

  const subTotal = data.items.reduce(
    (acc, item) => acc + item.price * item.quantity,
    0
  );
  const discount = data.discounted ? data.discount : 0;
  const tax = data.taxable ? subTotal * 0.18 : 0;
  const grandTotal = subTotal + tax - discount;

  const [company, setCompany] = useState(null);
  const [job, setJob] = useState(data.job);
  const [customer, setCustomer] = useState(null);
  const [vehicle, setVehicle] = useState(null);
  const [partsAndServices, setPartsAndServices] = useState(null);
  const [units, setUnits] = useState([]);
  customer && 

  useEffect(() => {
    const partsAndServicesDB = new PouchDb("parts_and_services");
    partsAndServicesDB
      .allDocs({
        include_docs: true,
        attachments: true,
        binary: true,
      })
      .then((res) => {
        setPartsAndServices(res.rows.filter(r => r.doc && !r.doc._id.startsWith('_')));
      });
  }, []);

  useEffect(() => {
    const jobsDB = new PouchDb("jobs");
    jobsDB.get(data.job.value, { binary: true }).then((res) => {
      setJob(res);
    });
  }, []);

  useEffect(() => {
    
    const customersDB = new PouchDb("customers");
    job &&
      job.customer &&
      customersDB
        .get(job.customer.value || job.customer._id, { binary: true })
        .then((res) => {
          
          setCustomer(res);
        });
  }, [job]);

  useEffect(() => {
    const vehiclesDB = new PouchDb("vehicles");
    job &&
      job.vehicle &&
      vehiclesDB.get(job.vehicle.value, { binary: true }).then((res) => {
        setVehicle(res);
      });
  }, [job]);

  useEffect(() => {
    const unitsDB = new PouchDb("units");
    unitsDB
      .allDocs({
        include_docs: true,
        binary: true,
      })
      .then((data) => {
        const docs = data.rows.filter(r => r.doc && !r.doc._id.startsWith('_')).map((d) => d.doc);
        setUnits(docs);
      });
  }, [job]);

  

  useEffect(() => {
    // Get organization ID from localStorage for better performance
    const storedOrganization = JSON.parse(localStorage.getItem(LOCAL_STORAGE_ORGANIZATION));

    if (storedOrganization && storedOrganization._id) {
      // Use getDocument with specific organization ID
      const CompanyDB = new PouchDb("organizations");
      CompanyDB.get(storedOrganization._id, {
        attachments: true,
        binary: true,
      }).then((comp) => {
        if (comp._attachments && comp._attachments.logo) {
          CompanyDB.getAttachment(comp._id, "logo").then((res) => {
            comp.logo = res;
            setCompany(comp);
          });
        } else {
          setCompany(comp);
        }
      }).catch((error) => {
        console.error("Error fetching organization:", error);
        // Fallback to old method
        CompanyDB.allDocs({
          include_docs: true,
          attachments: true,
          binary: true,
        }).then((data) => {
          const filteredRows = data.rows.filter(r => r.doc && !r.doc._id.startsWith('_'));
          if (filteredRows.length > 0) {
            let comp = filteredRows[0].doc;
            if (comp._attachments && comp._attachments.logo) {
              CompanyDB.getAttachment(comp._id, "logo").then((res) => {
                comp.logo = res;
                setCompany(comp);
              });
            } else {
              setCompany(comp);
            }
          }
        });
      });
    } else {
      // Fallback to old method if no organization in localStorage
      const CompanyDB = new PouchDb("organizations");
      CompanyDB.allDocs({
        include_docs: true,
        attachments: true,
        binary: true,
      }).then((data) => {
        const filteredRows = data.rows.filter(r => r.doc && !r.doc._id.startsWith('_'));
        if (filteredRows.length > 0) {
          let comp = filteredRows[0].doc;
          if (comp._attachments && comp._attachments.logo) {
            CompanyDB.getAttachment(comp._id, "logo").then((res) => {
              comp.logo = res;
              setCompany(comp);
            });
          } else {
            setCompany(comp);
          }
        }
      });
    }
  }, []);

  const emptyRows = [];

  for (
    let index = 0;
    index < (appSettings.documentHeader === "V2" ? 28 : 24) - data.items.length;
    index++
  ) {
    emptyRows.push({});
  }

  return !company || !customer || !vehicle ? null : (
    <div className="tm_invoice_wrap">
      <div className="tm_invoice tm_style1" id="tm_download_section">
        <div className={`tm_invoice_in ${job.job_type === "repeat_job" ? "repeat_job_watermark" : ""}`}>
          {appSettings &&
            appSettings.documentHeader &&
            appSettings.documentHeader === "V2" && (
              <>
                <Flex vertical={false} justify="space-between">
                  <RenderBlob blob={company.logo} size={150} />
                  <div>
                    <strong style={{ fontSize: 25, fontWeight: "bold" }}>
                      {title.toUpperCase()}
                    </strong>
                    <p class="tm_invoice_number tm_m0 tm_f11">
                      {title} No: <b class="tm_primary_color">{data._id}</b>
                      <br /> Date:{" "}
                      <b class="tm_primary_color">
                        {moment(data.date).format("DD MMM YYYY")}
                      </b>
                    </p>
                  </div>
                  <p class="tm_mb2 tm_f12">
                    {company.address}
                    <br />
                    Tel: {company.phone} / {company.alternative_phone} <br />
                    Email: {company.email} <br />
                    {company.website && company.website}
                  </p>
                </Flex>
                <Divider />
                <Flex vertical={false} justify="space-between">
                  <div class="tm_mb2 tm_f10">
                    <p class="tm_mb2">
                      <b class="tm_primary_color">To:</b>
                    </p>
                    <p>
                      <b style={{ fontSize: 15 }}>{customer.name}</b>
                      <br />
                      {customer.phone}{" "}
                      {customer.alternative_phone &&
                        `/ ${customer.alternative_phone}`}{" "}
                      <br />
                      {customer.email && (
                        <>
                          {customer.email} <br />
                        </>
                      )}
                      {customer.address && (
                        <>
                          {customer.address} <br />
                        </>
                      )}
                    </p>
                  </div>
                  <div class="tm_mb2 tm_f10">
                    {job.vehicle.label}
                    {job.millage && (
                      <>
                        <br />
                        Mileage :{" "}
                        <strong>{numberFormat(job.millage)} km</strong>
                      </>
                    )}
                  </div>
                </Flex>
              </>
            )}

          {(!appSettings.documentHeader ||
            appSettings.documentHeader === "V1") && (
              <Flex vertical={true}>
                <center>
                  <RenderBlob blob={company.logo} size={250} />
                  <p class="tm_f12">
                    {company.address}
                    <br />
                    Tel: {company.phone} / {company.alternative_phone} <br />
                    Email: {company.email} <br />
                    {company.website && company.website}
                  </p>
                </center>
                <Flex vertical={false} justify="space-between">
                  <div>
                    <p class="tm_mb2 tm_f12">
                      <b class="tm_primary_color">To:</b>
                    </p>
                    <p>
                      <b style={{ fontSize: 15 }}>{customer.name}</b>
                      <br />
                      {customer.phone}{" "}
                      {customer.alternative_phone &&
                        `/ ${customer.alternative_phone}`}{" "}
                      <br />
                      {customer.email && (
                        <>
                          {customer.email} <br />
                        </>
                      )}
                      {customer.address && (
                        <>
                          {customer.address} <br />
                        </>
                      )}
                      {customer._id && (
                        <>
                          Customer ID : <strong>{customer._id} <br /></strong>
                        </>
                      )}
                      <hr />
                      {job.vehicle.label}
                      {job.millage && (
                        <>
                          <br />
                          Mileage :{" "}
                          <strong>{numberFormat(job.millage)} km</strong>
                        </>
                      )}
                    </p>
                  </div>
                  <div>
                    <strong style={{ fontSize: 30, fontWeight: "bold" }}>
                      {title.toUpperCase()}
                    </strong>
                    <p class="tm_invoice_number tm_m0">
                      {title} No: <b class="tm_primary_color">{data._id}</b>
                    </p>
                    <p class="tm_invoice_date tm_m0">
                      Date:{" "}
                      <b class="tm_primary_color">
                        {moment(data.date).format("DD MMM YYYY")}
                      </b>
                    </p>
                    <p class="tm_invoice_number tm_m0">
                      Job ID: <b class="tm_primary_color">{job._id}</b>
                    </p>
                  </div>
                </Flex>
              </Flex>
            )}

          <div className="tm_table tm_style">
            <center style={{ minHeight: 440 }}>
              <div
                className="tm_border"
              >
                <div className="main">
                  <table>
                    <thead>
                      <tr>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_center">
                          S/N
                        </th>
                        <th className="tm_width_3 tm_semi_bold tm_primary_color tm_gray_bg">
                          Description
                        </th>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_left">
                          Qty
                        </th>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_right">
                          Rate
                        </th>
                        <th className="tm_width_1 tm_semi_bold tm_primary_color tm_gray_bg tm_text_right">
                          Total
                        </th>
                      </tr>
                    </thead>
                    <tbody>
                      {data.items.map((item, index) => {
                        const PartUnit = partsAndServices.find(
                          (part) => part.id === item.item.value
                        );

                        

                        const unit =
                          PartUnit && PartUnit.doc && PartUnit.doc.unit
                            ? units.find(
                              (u) => u._id === PartUnit.doc.unit.value
                            )?.abbreviation
                            : "";

                        return (
                          <>
                            <tr className="main">
                              <td className="tm_width_1 tm_text_center">
                                {index + 1}
                              </td>
                              <td className="tm_width_3">{item.item.label}</td>
                              <td className="tm_width_1 tm_text_left">
                                {item.quantity}{" "}
                                {item.quantity === 1 || unit === ""
                                  ? unit
                                  : unit + "s"}
                              </td>
                              <td className="tm_width_1 tm_text_right">
                                {numberFormat(item.price)}
                              </td>
                              <td className="tm_width_1 tm_text_right">
                                {numberFormat(item.price * item.quantity)}
                              </td>
                            </tr>
                          </>
                        );
                      })}
                      {emptyRows.map((item, index) => {
                        return (
                          <tr className="main">
                            <td className="tm_width_1 tm_text_center">
                              {" "}
                              &nbsp;
                            </td>
                            <td className="tm_width_3"></td>
                            <td className="tm_width_1 tm_text_left"></td>
                            <td className="tm_width_1 tm_text_right"></td>
                            <td className="tm_width_1 tm_text_right"></td>
                          </tr>
                        );
                      })}
                    </tbody>
                  </table>
                </div>
              </div>
            </center>
            <div className="tm_invoice_footer tm_mb20 tm_m0_md">
              <div className="tm_left_footer">
                {appSettings.mobile_wallet_settings && (
                  <p className="tm_mb2 tm_f12">
                    <b className="tm_primary_color">
                      Mobile Wallet Payment Method:{" "}
                    </b>
                    <br />
                    {appSettings.mobile_wallet_settings.map((item) => (
                      <div>
                        {item.mobile_wallet} : <b>{item.id}</b>
                      </div>
                    ))}
                    <br />
                  </p>
                )}
                {appSettings.acc_no && (
                  <p className="tm_m0 tm_f12">
                    <b className="tm_primary_color">Bank Details: </b>
                    <br />
                    {appSettings.acc_name} : <b>{appSettings.acc_no}</b>
                    <br />
                    {appSettings.bank_name} - {appSettings.bank_address} Branch
                  </p>
                )}
              </div>
              <div className="tm_right_footer">
                <table>
                  <tbody>
                    <tr>
                      <td className="tm_width_3 tm_primary_color tm_border_none tm_bold tm_f15">
                        Subtotal
                      </td>
                      <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_bold tm_f15">
                        {appSettings.currency} {numberFormat(subTotal)}
                      </td>
                    </tr>
                    <tr>
                      <td className="tm_width_3 tm_primary_color tm_border_none tm_pt0 tm_f15">
                        Tax <span className="tm_ternary_color">(18%)</span>
                      </td>
                      <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_pt0 tm_f15">
                        {appSettings.currency} {numberFormat(tax)}
                      </td>
                    </tr>
                    {data.discounted && (
                      <tr>
                        <td className="tm_width_3 tm_primary_color tm_border_none tm_pt0 tm_f15">
                          Discount
                          {data.discount_description && (
                            <span className="tm_secondary_color">{` (${data.discount_description})`}</span>
                          )}
                        </td>
                        <td className="tm_width_3 tm_primary_color tm_text_right tm_border_none tm_pt0 tm_f15">
                          {appSettings.currency} {numberFormat(discount)}
                        </td>
                      </tr>
                    )}
                    <tr className="tm_border_top tm_border_bottom">
                      <td className="tm_width_3 tm_border_top_0 tm_bold tm_f15 tm_primary_color">
                        Grand Total{" "}
                      </td>
                      <td className="tm_width_3 tm_border_top_0 tm_bold tm_f15 tm_primary_color tm_text_right">
                        {appSettings.currency} {numberFormat(grandTotal)}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            <center>
              <strong style={{ fontSize: "12px" }}>{bottomText}</strong>
            </center>
            <div className="tm_invoice_footer tm_type1">
              <DocumentFooter />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const PrintableQuotation = (props) => {
  return (
    <>
      <PrintComponents
        trigger={
          <FloatButton icon={<PrinterOutlined />} tooltip={<div>Print</div>} />
        }
      >
        <Quotation {...props} />
      </PrintComponents>
      <Quotation {...props} />
    </>
  );
};

export default PrintableQuotation;