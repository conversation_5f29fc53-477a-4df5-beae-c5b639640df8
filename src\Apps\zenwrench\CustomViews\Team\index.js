import React, { useEffect, useState } from "react";
import "./css.css";
import {
  Descriptions,
  Divider,
  Tabs,
  Layout,
  Typography,
  Tag,
  Button,
  Flex,
  FloatButton,
  Card,
  Row,
  Col,
  Avatar,
  List,
  Space,
} from "antd";
import {
  TeamOutlined,
  UserOutlined,
  PrinterOutlined,
  ScheduleOutlined,
  TrophyOutlined,
  ToolOutlined,
} from "@ant-design/icons";
import moment from "moment";
import PrintComponents from "react-print-components";
import ViewTable from "../../../../Components/ViewTable";
import RenderBlob from "../../../../Components/RenderBlob";
import { PageHeader } from "@ant-design/pro-components";

const { Content } = Layout;
const { TabPane } = Tabs;
const { Title, Text, Paragraph } = Typography;

const Team = (props) => {
  const { data, pouchDatabase, databasePrefix, ...rest } = props;
  const [leader, setLeader] = useState(null);
  const [members, setMembers] = useState([]);
  const [company, setCompany] = useState(null);
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(true);

  // Shared props for ViewTable components
  const sharedProps = {
    pouchDatabase: props.pouchDatabase,
    databasePrefix: props.databasePrefix,
    CRUD_USER: props.CRUD_USER,
    userPermissions: props.userPermissions,
    modules: props.modules,
    modulesProperties: props.modulesProperties,
    filterID: { column: "assigned_to", id: data._id },
  };

  // Fetch related data
  useEffect(() => {
    const fetchData = async () => {
      if (!pouchDatabase || databasePrefix === undefined) {
        setLoading(false);
        return;
      }

      try {
        // Fetch leader data
        if (data.leader && data.leader.value) {
          const leaderData = await pouchDatabase("users", databasePrefix).getDocument(data.leader.value);
          setLeader(leaderData);
        }

        // Fetch members data
        if (data.members && data.members.length > 0) {
          const memberPromises = data.members.map(member =>
            pouchDatabase("users", databasePrefix).getDocument(member.value).catch(err => {
              console.error(`Error fetching member ${member.value}:`, err);
              return { _id: member.value, first_name: "Unknown", last_name: "User" };
            })
          );
          const membersData = await Promise.all(memberPromises);
          setMembers(membersData);
        }

        // Fetch company data
        const companyData = await pouchDatabase("organizations", databasePrefix).getAllData();
        if (companyData && companyData.length > 0) {
          setCompany(companyData[0]);
        }

        // Fetch tasks assigned to this team
        const allTasks = await pouchDatabase("tasks", databasePrefix).getAllData();
        const filteredTasks = allTasks.filter(task =>
          task.assigned_to && task.assigned_to.value === data._id
        ).slice(0, 10);

        setTasks(filteredTasks);
        setLoading(false);
      } catch (error) {
        console.error("Error fetching data:", error);
        setLoading(false);
      }
    };

    fetchData();
  }, [data, pouchDatabase, databasePrefix]);

  // Get avatar color based on name
  const getAvatarColor = (name) => {
    const colors = ['#f56a00', '#7265e6', '#ffbf00', '#00a2ae', '#87d068'];
    let sum = 0;
    if (name) {
      for (let i = 0; i < name.length; i++) {
        sum += name.charCodeAt(i);
      }
    }
    return colors[sum % colors.length];
  };

  // Get initials from name
  const getInitials = (firstName, lastName) => {
    return `${firstName ? firstName[0] : ''}${lastName ? lastName[0] : ''}`;
  };

  // Printable team component
  const PrintableTeam = () => (
    <div style={{ padding: "20px", maxWidth: "800px", margin: "0 auto" }}>
      {company && (
        <Flex align="center" justify="space-between" style={{ marginBottom: "20px" }}>
          <div>
            <RenderBlob blob={company.logo} size={150} />
          </div>
          <div style={{ textAlign: "center" }}>
            <Title level={2}>TEAM PROFILE</Title>
            <Text>Team: {data.name}</Text>
          </div>
          <div style={{ textAlign: "right" }}>
            <Text>
              {company.address}
              <br />
              Tel: {company.phone}
              <br />
              Email: {company.email}
            </Text>
          </div>
        </Flex>
      )}

      <Divider />

      <Title level={4}>Team Information</Title>
      <Descriptions column={1} bordered>
        <Descriptions.Item label="Team Name">{data.name}</Descriptions.Item>
        <Descriptions.Item label="Team Leader">
          {leader ? `${leader.first_name} ${leader.last_name}` : data.leader?.label || "Not assigned"}
        </Descriptions.Item>
        <Descriptions.Item label="Number of Members">
          {members.length}
        </Descriptions.Item>
      </Descriptions>

      <Divider />

      <Title level={4}>Team Members</Title>
      <table className="team-members-table">
        <thead>
          <tr>
            <th>Name</th>
            <th>Position</th>
            <th>Contact</th>
          </tr>
        </thead>
        <tbody>
          {members.map((member, index) => (
            <tr key={index}>
              <td>{`${member.first_name} ${member.last_name}`}</td>
              <td>{member.position || "Team Member"}</td>
              <td>{member.phone || member.email || "N/A"}</td>
            </tr>
          ))}
        </tbody>
      </table>

      <Divider />

      <div style={{ marginTop: "50px", textAlign: "center" }}>
        <p>Generated on: {moment().format("DD MMM YYYY, HH:mm")}</p>
      </div>
    </div>
  );

  if (loading) {
    return <div>Loading team information...</div>;
  }

  return (
    <>
      <PageHeader
        backIcon={<TeamOutlined style={{ fontSize: 30 }} />}
        onBack={() => window.history.back()}
        title={data.name}
        subTitle={leader ? `Led by ${leader.first_name} ${leader.last_name}` : ""}
        extra={[
          <PrintComponents
            key="print"
            trigger={
              <Button icon={<PrinterOutlined />} type="primary">
                Print Team Profile
              </Button>
            }
          >
            <PrintableTeam />
          </PrintComponents>,
        ]}
      />

      <Content style={{ padding: "0 24px 24px" }}>
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={16}>
            <Card title={<><TeamOutlined /> Team Overview</>} className="team-card">
              <Descriptions bordered column={{ xxl: 2, xl: 2, lg: 2, md: 1, sm: 1, xs: 1 }}>
                <Descriptions.Item label="Team Name">{data.name}</Descriptions.Item>
                <Descriptions.Item label="Number of Members">
                  {members.length}
                </Descriptions.Item>
                <Descriptions.Item label="Team Leader">
                  {leader ? `${leader.first_name} ${leader.last_name}` : data.leader?.label || "Not assigned"}
                </Descriptions.Item>
                <Descriptions.Item label="Active Tasks">
                  {tasks.filter(task => task.status === "processing").length}
                </Descriptions.Item>
              </Descriptions>
            </Card>
          </Col>

          <Col xs={24} lg={8}>
            <Card title={<><TrophyOutlined /> Team Leader</>} className="team-card">
              {leader ? (
                <Flex align="center" gap="middle">
                  <Avatar
                    size={64}
                    style={{ backgroundColor: getAvatarColor(leader.first_name) }}
                  >
                    {getInitials(leader.first_name, leader.last_name)}
                  </Avatar>
                  <div>
                    <Text strong style={{ fontSize: 16 }}>
                      {leader.first_name} {leader.last_name}
                    </Text>
                    <br />
                    <Text type="secondary">{leader.position || "Team Leader"}</Text>
                    <br />
                    <Text>{leader.phone || leader.email}</Text>
                  </div>
                </Flex>
              ) : (
                <Text type="secondary">No team leader assigned</Text>
              )}
            </Card>
          </Col>
        </Row>

        <Row gutter={[24, 24]} style={{ marginTop: 24 }}>
          <Col xs={24}>
            <Card title={<><UserOutlined /> Team Members</>} className="team-card">
              <List
                grid={{ gutter: 16, xs: 1, sm: 2, md: 3, lg: 4, xl: 4, xxl: 6 }}
                dataSource={members}
                renderItem={(member) => (
                  <List.Item>
                    <Card className="member-card">
                      <Flex vertical align="center" gap="small">
                        <Avatar
                          size={64}
                          style={{ backgroundColor: getAvatarColor(member.first_name) }}
                        >
                          {getInitials(member.first_name, member.last_name)}
                        </Avatar>
                        <Text strong>
                          {member.first_name} {member.last_name}
                        </Text>
                        <Text type="secondary">{member.position || "Team Member"}</Text>
                        <Text>{member.phone || member.email || ""}</Text>
                      </Flex>
                    </Card>
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        </Row>

        <Divider />

        <Tabs defaultActiveKey="tasks">
          <TabPane
            tab={
              <span>
                <ScheduleOutlined /> Assigned Tasks
              </span>
            }
            key="tasks"
          >
            <ViewTable
              {...sharedProps}
              {...props.modules.tasks}
              {...props.modulesProperties.tasks}
            />
          </TabPane>
        </Tabs>
      </Content>

      <FloatButton
        icon={<PrinterOutlined />}
        type="primary"
        tooltip="Print Team Profile"
        onClick={() => document.getElementById("print-button").click()}
        style={{ right: 94 }}
      />

      {/* Hidden print button for FloatButton to trigger */}
      <div style={{ display: "none" }}>
        <PrintComponents
          trigger={<Button id="print-button">Print</Button>}
        >
          <PrintableTeam />
        </PrintComponents>
      </div>
    </>
  );
};

export default Team;
