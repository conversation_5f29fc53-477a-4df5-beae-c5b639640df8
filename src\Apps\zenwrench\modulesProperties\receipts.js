import Receipt from "../CustomViews/Receipt";
import AppDatabase from "../../../Utils/AppDatabase";
export default {
  CustomView: (data) => <Receipt {...data} title="Receipt" />,

  beforeSave: async (values, { pouchDatabase, databasePrefix }) => {
    // If account is not provided, try to use default receipt account from settings
    if (!values.account || !values.account.value) {
      try {
        // Get settings document
        const settingsDoc = await pouchDatabase('settings', databasePrefix)
          .getAllData()
          .then(data => data[0]);

        // If settings exist and default_receipt_account is set
        if (settingsDoc && settingsDoc.default_receipt_account) {
          // Get the account details
          const accountDoc = await pouchDatabase('accounts', databasePrefix)
            .get(settingsDoc.default_receipt_account.value);

          // Set the account in the values
          values.account = {
            label: accountDoc.name,
            value: accountDoc._id,
            ...accountDoc
          };
        }
      } catch (error) {
        console.error('Error fetching default receipt account:', error);
        // Continue without setting default account
      }
    }

    return values;
  },

  afterSave: async (data, { pouchDatabase, databasePrefix, CRUD_USER }) => {
    // Check if receipt has an invoice attached
    if (data.invoice && data.invoice.value) {
      // Get the invoice document
      const invoiceDoc = await pouchDatabase(
        'invoices',
        databasePrefix
      ).get(data.invoice.value);

      // Check if invoice has a job attached
      if (invoiceDoc.job && invoiceDoc.job.value) {
        // Get the job document
        const jobDoc = await pouchDatabase(
          'jobs',
          databasePrefix
        ).get(invoiceDoc.job.value);

        // Create status update for receipt creation
        const statusUpdate = {
          status: "Payment Received",
          date: new Date().toISOString(),
          updatedBy: CRUD_USER,
          notes: `Payment of ${data.amount} received via ${data.pMethod || 'unspecified method'}${data.withholding ? ' (with withholding tax)' : ''
            }`
        };

        // Update the job with new status
        await pouchDatabase(
          'jobs',
          databasePrefix
        ).saveDocument({
          ...jobDoc,
          job_status: [...(jobDoc.job_status || []), statusUpdate]
        }, CRUD_USER);
      }
    }
  },

  buffResults: async (results, database, databasePrefix, collection, SELECTED_BRANCH) => {
    const buffedReceipts = []
    const invoicesDB = AppDatabase("invoices", databasePrefix);

    try {
      const invoices = await invoicesDB.getAllData();

      results.forEach(result => {
        if (result.invoice) {
          const invoice = invoices.find(invoice => invoice._id === result.invoice.value)

          const invoiceAmount = invoice ? invoice.items.reduce(
            (pv, cv) =>
              pv +
              (cv.price ? cv.price : 0) * (cv.quantity ? cv.quantity : 0),
            0
          ) : 0;
          const taxAmount = invoice ? (invoice.taxable ? invoiceAmount * 0.18 : 0) : 0;

          const receipt = {
            ...result,
            withheldAmount: (result.withholding && invoice.taxable) ?
              (parseFloat(invoiceAmount) + parseFloat(taxAmount)) - parseFloat(result.amount)
              : 0,
            // Add any additional properties you want to include in the receipt
          };
          buffedReceipts.push(receipt);
        } else {
          buffedReceipts.push(result);
        }
      });

      return buffedReceipts;
    } catch (error) {
      console.error("Error in receipts buffResults:", error);
      return results; // Return original results if buffing fails
    }
  },
  print: true,
};
